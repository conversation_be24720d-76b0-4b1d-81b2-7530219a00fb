/**
 * 应用常量定义模块
 * 定义系统中使用的所有常量和枚举值
 */

// HTTP状态码
const HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    METHOD_NOT_ALLOWED: 405,
    CONFLICT: 409,
    INTERNAL_SERVER_ERROR: 500,
    SERVICE_UNAVAILABLE: 503
};

// 响应消息
const RESPONSE_MESSAGES = {
    SUCCESS: '操作成功',
    CREATED: '创建成功',
    BAD_REQUEST: '请求参数错误',
    UNAUTHORIZED: '未授权访问',
    FORBIDDEN: '禁止访问',
    NOT_FOUND: '资源未找到',
    METHOD_NOT_ALLOWED: '请求方法不允许',
    INTERNAL_ERROR: '服务器内部错误',
    SERVICE_UNAVAILABLE: '服务暂时不可用',
  
    // 业务相关消息
    SONG_NOT_FOUND: '歌曲未找到',
    SEARCH_FAILED: '搜索失败',
    UNLOCK_FAILED: '解锁失败',
    INVALID_SONG_ID: '无效的歌曲ID',
    INVALID_SEARCH_PARAMS: '无效的搜索参数',
    NO_AVAILABLE_SOURCE: '没有可用的音源',
    METADATA_UNAVAILABLE: '元数据不可用'
};

// 支持的音源列表
const MUSIC_SOURCES = {
    QQ: 'qq',
    KUGOU: 'kugou',
    KUWO: 'kuwo',
    MIGU: 'migu',
    JOOX: 'joox',
    YOUTUBE: 'youtube',
    YTDLP: 'ytdlp',
    BILIBILI: 'bilibili',
    BILIVIDEO: 'bilivideo'
};

// 音源显示名称映射
const SOURCE_DISPLAY_NAMES = {
    [MUSIC_SOURCES.QQ]: 'QQ音乐',
    [MUSIC_SOURCES.KUGOU]: '酷狗音乐',
    [MUSIC_SOURCES.KUWO]: '酷我音乐',
    [MUSIC_SOURCES.MIGU]: '咪咕音乐',
    [MUSIC_SOURCES.JOOX]: 'JOOX',
    [MUSIC_SOURCES.YOUTUBE]: 'YouTube',
    [MUSIC_SOURCES.YTDLP]: 'YouTube (yt-dlp)',
    [MUSIC_SOURCES.BILIBILI]: 'B站音乐',
    [MUSIC_SOURCES.BILIVIDEO]: 'B站视频'
};

// 搜索类型
const SEARCH_TYPES = {
    ID: 'id',
    KEYWORD: 'keyword',
    BATCH: 'batch'
};

// 音质等级
const QUALITY_LEVELS = {
    LOW: 128000,      // 128kbps
    STANDARD: 192000, // 192kbps
    HIGH: 320000,     // 320kbps
    LOSSLESS: 999000  // 无损
};

// 音质等级显示名称
const QUALITY_DISPLAY_NAMES = {
    [QUALITY_LEVELS.LOW]: '标准',
    [QUALITY_LEVELS.STANDARD]: '较高',
    [QUALITY_LEVELS.HIGH]: '极高',
    [QUALITY_LEVELS.LOSSLESS]: '无损'
};

// 日志级别
const LOG_LEVELS = {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug'
};

// API限制
const API_LIMITS = {
    MAX_BATCH_SIZE: 20,        // 批量操作最大数量
    MAX_SEARCH_RESULTS: 50,    // 最大搜索结果数
    REQUEST_TIMEOUT: 30000,    // 请求超时时间(毫秒)
    MAX_KEYWORD_LENGTH: 100    // 搜索关键词最大长度
};

// 缓存配置
const CACHE_CONFIG = {
    METADATA_TTL: 3600,        // 元数据缓存时间(秒)
    SEARCH_TTL: 1800,          // 搜索结果缓存时间(秒)
    UNLOCK_TTL: 7200           // 解锁结果缓存时间(秒)
};

// 错误代码
const ERROR_CODES = {
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    SONG_NOT_FOUND: 'SONG_NOT_FOUND',
    SEARCH_FAILED: 'SEARCH_FAILED',
    UNLOCK_FAILED: 'UNLOCK_FAILED',
    SOURCE_UNAVAILABLE: 'SOURCE_UNAVAILABLE',
    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
    INTERNAL_ERROR: 'INTERNAL_ERROR'
};

module.exports = {
    HTTP_STATUS,
    RESPONSE_MESSAGES,
    MUSIC_SOURCES,
    SOURCE_DISPLAY_NAMES,
    SEARCH_TYPES,
    QUALITY_LEVELS,
    QUALITY_DISPLAY_NAMES,
    LOG_LEVELS,
    API_LIMITS,
    CACHE_CONFIG,
    ERROR_CODES
};
