/**
 * HTML测试页面自动化测试脚本
 * 使用Playwright进行端到端测试
 */

const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://localhost:3000';
const TEST_TIMEOUT = 30000;

test.describe('音乐解锁服务HTML测试页面', () => {
    test.beforeEach(async ({ page }) => {
        // 设置超时时间
        test.setTimeout(TEST_TIMEOUT);
        
        // 访问测试页面
        await page.goto(BASE_URL);
        
        // 等待页面加载完成
        await page.waitForLoadState('networkidle');
    });

    test('页面基本元素加载', async ({ page }) => {
        // 检查页面标题
        await expect(page).toHaveTitle(/音乐解锁服务/);
        
        // 检查主要功能模块是否存在
        await expect(page.locator('#song-tab')).toBeVisible();
        await expect(page.locator('#search-tab')).toBeVisible();
        await expect(page.locator('#unlock-tab')).toBeVisible();
        await expect(page.locator('#sources-tab')).toBeVisible();

        // 检查输入框和按钮
        await expect(page.locator('#song-id')).toBeVisible();
        await expect(page.locator('#search-query')).toBeVisible();
        await expect(page.locator('#unlock-ids')).toBeVisible();
    });

    test('搜索功能测试', async ({ page }) => {
        // 切换到搜索标签页
        await page.click('button[data-tab="search"]');

        // 测试关键词搜索
        await page.fill('#search-query', '周杰伦');
        await page.click('button:has-text("开始搜索")');

        // 等待结果显示
        await page.waitForSelector('#search-result', { timeout: 10000 });

        // 检查结果是否显示
        const searchResult = await page.locator('#search-result').textContent();
        expect(searchResult).toBeTruthy();
        expect(searchResult.length).toBeGreaterThan(0);
    });

    test('歌曲信息获取功能', async ({ page }) => {
        // 确保在歌曲标签页
        await page.click('button[data-tab="song"]');

        // 测试歌曲信息获取
        await page.fill('#song-id', '418602084');
        await page.click('button:has-text("获取歌曲信息")');

        // 等待结果显示
        await page.waitForSelector('#song-result', { timeout: 15000 });

        // 检查结果是否显示
        const songResult = await page.locator('#song-result').textContent();
        expect(songResult).toBeTruthy();
    });

    test('歌曲解锁功能', async ({ page }) => {
        // 切换到解锁标签页
        await page.click('button[data-tab="unlock"]');

        // 测试歌曲解锁
        await page.fill('#unlock-ids', '418602084');
        await page.click('button:has-text("批量解锁")');

        // 等待结果显示
        await page.waitForSelector('#unlock-result', { timeout: 20000 });

        // 检查结果是否显示
        const unlockResult = await page.locator('#unlock-result').textContent();
        expect(unlockResult).toBeTruthy();
    });

    test('音源管理功能', async ({ page }) => {
        // 切换到音源管理标签页
        await page.click('button[data-tab="sources"]');

        // 测试获取音源列表
        await page.click('button:has-text("获取音源列表")');

        // 等待结果显示
        await page.waitForSelector('#sources-result', { timeout: 10000 });

        // 检查结果是否显示
        const sourcesResult = await page.locator('#sources-result').textContent();
        expect(sourcesResult).toBeTruthy();
    });

    test('系统状态检查', async ({ page }) => {
        // 检查服务状态指示器
        const statusIndicator = await page.locator('#service-status');
        await expect(statusIndicator).toBeVisible();

        // 检查API版本信息
        const apiVersion = await page.locator('#api-version');
        await expect(apiVersion).toBeVisible();
    });

    test('错误处理测试', async ({ page }) => {
        // 确保在歌曲标签页
        await page.click('button[data-tab="song"]');

        // 测试无效ID
        await page.fill('#song-id', 'invalid');
        await page.click('button:has-text("获取歌曲信息")');

        // 等待结果显示
        await page.waitForSelector('#song-result', { timeout: 10000 });

        // 检查错误信息是否正确显示
        const songResult = await page.locator('#song-result').textContent();
        expect(songResult).toBeTruthy();
    });

    test('响应时间性能测试', async ({ page }) => {
        const startTime = Date.now();

        // 确保在歌曲标签页
        await page.click('button[data-tab="song"]');

        // 执行歌曲信息获取操作
        await page.fill('#song-id', '418602084');
        await page.click('button:has-text("获取歌曲信息")');

        // 等待结果显示
        await page.waitForSelector('#song-result', { timeout: 15000 });

        const endTime = Date.now();
        const responseTime = endTime - startTime;

        // 检查响应时间是否在合理范围内（小于15秒）
        expect(responseTime).toBeLessThan(15000);
        console.log(`歌曲信息获取响应时间: ${responseTime}ms`);
    });

    test('界面交互测试', async ({ page }) => {
        // 测试标签页切换功能
        await page.click('button[data-tab="search"]');
        await expect(page.locator('#search-tab')).toHaveClass(/active/);

        await page.click('button[data-tab="unlock"]');
        await expect(page.locator('#unlock-tab')).toHaveClass(/active/);

        await page.click('button[data-tab="sources"]');
        await expect(page.locator('#sources-tab')).toHaveClass(/active/);

        // 测试输入框功能
        await page.click('button[data-tab="song"]');
        await page.fill('#song-id', '418602084');
        const inputValue = await page.locator('#song-id').inputValue();
        expect(inputValue).toBe('418602084');
    });
});

test.describe('API响应格式测试', () => {
    test('JSON响应格式验证', async ({ page }) => {
        // 监听网络请求
        const responses = [];
        page.on('response', response => {
            if (response.url().includes('/api/')) {
                responses.push(response);
            }
        });
        
        // 执行搜索操作
        await page.fill('#songId', '418602084');
        await page.click('button:has-text("搜索歌曲")');
        
        // 等待请求完成
        await page.waitForTimeout(3000);
        
        // 检查API响应
        expect(responses.length).toBeGreaterThan(0);
        
        for (const response of responses) {
            expect(response.status()).toBeLessThan(500);
            const contentType = response.headers()['content-type'];
            if (contentType) {
                expect(contentType).toContain('application/json');
            }
        }
    });
});
