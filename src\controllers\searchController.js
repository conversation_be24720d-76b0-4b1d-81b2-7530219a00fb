/**
 * 搜索控制器
 * 处理音乐搜索相关的API请求
 */

const { search, searchById, searchByKeyword, searchBatch } = require('../services/searchService');
const { success, badRequest, searchResults } = require('../utils/response');
const { asyncHandler } = require('../middleware/errorHandler');
const { logBusiness } = require('../middleware/logger');
const { SEARCH_TYPES, API_LIMITS } = require('../utils/constants');

/**
 * 统一搜索接口
 * POST /api/search
 */
const searchSongs = asyncHandler(async (req, res) => {
    const { type, query, sources, page = 1, pageSize = 20, includeAlbum = false, includeUnlockUrl = false } = req.body;

    logBusiness('搜索请求', { type, query, sources, page, pageSize });

    // 验证搜索类型
    if (!Object.values(SEARCH_TYPES).includes(type)) {
        return badRequest(res, `不支持的搜索类型: ${type}`);
    }

    // 验证查询参数
    if (!query) {
        return badRequest(res, '搜索查询不能为空');
    }

    // 验证分页参数
    if (page < 1 || pageSize < 1 || pageSize > 100) {
        return badRequest(res, '无效的分页参数');
    }

    const searchParams = {
        type,
        query,
        sources,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        includeAlbum,
        includeUnlockUrl
    };

    const result = await search(searchParams);

    // 使用专门的搜索结果响应格式
    searchResults(res, result.results, result.query, result.total, result.took);
});

/**
 * ID搜索接口
 * GET /api/search/id/:songId
 */
const searchBySongId = asyncHandler(async (req, res) => {
    const { songId } = req.params;
    const { sources, includeUnlockUrl = false } = req.query;

    logBusiness('ID搜索请求', { songId, sources });

    const sourcesArray = sources ? sources.split(',').map(s => s.trim()) : null;

    const result = await searchById(songId, sourcesArray, {
        includeUnlockUrl: includeUnlockUrl === 'true'
    });

    searchResults(res, result.results, result.query, result.total, result.took);
});

/**
 * 关键词搜索接口
 * GET /api/search/keyword
 */
const searchByKeywords = asyncHandler(async (req, res) => {
    const { 
        q: keyword, 
        sources, 
        page = 1, 
        pageSize = 20, 
        includeAlbum = false,
        includeUnlockUrl = false 
    } = req.query;

    logBusiness('关键词搜索请求', { keyword, sources, page, pageSize });

    // 验证关键词
    if (!keyword || keyword.trim().length === 0) {
        return badRequest(res, '搜索关键词不能为空');
    }

    if (keyword.length > API_LIMITS.MAX_KEYWORD_LENGTH) {
        return badRequest(res, `搜索关键词过长，最大长度: ${API_LIMITS.MAX_KEYWORD_LENGTH}`);
    }

    // 验证分页参数
    if (page < 1 || pageSize < 1 || pageSize > 100) {
        return badRequest(res, '无效的分页参数');
    }

    const sourcesArray = sources ? sources.split(',').map(s => s.trim()) : null;

    const result = await searchByKeyword(keyword.trim(), sourcesArray, {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        includeAlbum: includeAlbum === 'true',
        includeUnlockUrl: includeUnlockUrl === 'true'
    });

    searchResults(res, result.results, result.query, result.total, result.took);
});

/**
 * 批量搜索接口
 * POST /api/search/batch
 */
const searchMultiple = asyncHandler(async (req, res) => {
    const { queries, sources, includeUnlockUrl = false } = req.body;

    logBusiness('批量搜索请求', { count: queries?.length, sources });

    // 验证查询列表
    if (!Array.isArray(queries) || queries.length === 0) {
        return badRequest(res, '查询列表不能为空');
    }

    if (queries.length > API_LIMITS.MAX_BATCH_SIZE) {
        return badRequest(res, `批量搜索数量超限，最大: ${API_LIMITS.MAX_BATCH_SIZE}`);
    }

    // 验证每个查询项
    for (const query of queries) {
        if (!query || (typeof query === 'string' && query.trim().length === 0)) {
            return badRequest(res, '查询列表中包含无效的查询项');
        }
    }

    const result = await searchBatch(queries, sources, {
        includeUnlockUrl
    });

    // 批量搜索返回特殊格式
    const response = {
        type: result.type,
        queries: result.queries,
        results: result.results,
        summary: {
            total: result.total,
            successCount: result.successCount,
            failedCount: result.failedCount,
            took: result.took
        },
        success: result.success,
        failed: result.failed
    };

    success(res, response, '批量搜索完成');
});

/**
 * 搜索建议接口
 * GET /api/search/suggest
 */
const getSearchSuggestions = asyncHandler(async (req, res) => {
    const { q: keyword, limit = 10 } = req.query;

    logBusiness('搜索建议请求', { keyword, limit });

    // 验证关键词
    if (!keyword || keyword.trim().length === 0) {
        return badRequest(res, '搜索关键词不能为空');
    }

    if (keyword.length > 50) {
        return badRequest(res, '搜索关键词过长');
    }

    // 验证限制数量
    if (limit < 1 || limit > 20) {
        return badRequest(res, '建议数量应在1-20之间');
    }

    // 这里应该实现搜索建议逻辑
    // 由于是演示项目，返回模拟数据
    const suggestions = generateMockSuggestions(keyword.trim(), parseInt(limit));

    success(res, {
        keyword: keyword.trim(),
        suggestions,
        count: suggestions.length
    }, '搜索建议获取成功');
});

/**
 * 热门搜索接口
 * GET /api/search/trending
 */
const getTrendingSearches = asyncHandler(async (req, res) => {
    const { limit = 20 } = req.query;

    logBusiness('热门搜索请求', { limit });

    // 验证限制数量
    if (limit < 1 || limit > 50) {
        return badRequest(res, '限制数量应在1-50之间');
    }

    // 模拟热门搜索数据
    const trending = generateMockTrending(parseInt(limit));

    success(res, {
        trending,
        count: trending.length,
        lastUpdated: new Date().toISOString()
    }, '热门搜索获取成功');
});

/**
 * 生成模拟搜索建议
 * @param {string} keyword - 搜索关键词
 * @param {number} limit - 建议数量
 * @returns {Array} 建议列表
 */
function generateMockSuggestions(keyword, limit) {
    const mockSuggestions = [
        '周杰伦',
        '稻香',
        '青花瓷',
        '夜曲',
        '告白气球',
        '七里香',
        '简单爱',
        '听妈妈的话',
        '菊花台',
        '东风破'
    ];

    // 过滤包含关键词的建议
    const filtered = mockSuggestions.filter(suggestion => 
        suggestion.includes(keyword) || keyword.includes(suggestion)
    );

    // 如果过滤结果不够，添加一些通用建议
    if (filtered.length < limit) {
        const additional = mockSuggestions.slice(0, limit - filtered.length);
        filtered.push(...additional);
    }

    return filtered.slice(0, limit).map(suggestion => ({
        text: suggestion,
        type: 'artist' // 可以是 'song', 'artist', 'album'
    }));
}

/**
 * 生成模拟热门搜索
 * @param {number} limit - 数量限制
 * @returns {Array} 热门搜索列表
 */
function generateMockTrending(limit) {
    const mockTrending = [
        { keyword: '周杰伦', count: 12580, trend: 'up' },
        { keyword: '稻香', count: 8960, trend: 'up' },
        { keyword: '青花瓷', count: 7340, trend: 'stable' },
        { keyword: '夜曲', count: 6120, trend: 'down' },
        { keyword: '告白气球', count: 5890, trend: 'up' },
        { keyword: '七里香', count: 4560, trend: 'stable' },
        { keyword: '简单爱', count: 3780, trend: 'up' },
        { keyword: '听妈妈的话', count: 3210, trend: 'down' },
        { keyword: '菊花台', count: 2890, trend: 'stable' },
        { keyword: '东风破', count: 2340, trend: 'up' }
    ];

    return mockTrending.slice(0, limit);
}

module.exports = {
    searchSongs,
    searchBySongId,
    searchByKeywords,
    searchMultiple,
    getSearchSuggestions,
    getTrendingSearches
};
