/**
 * 音源管理控制器
 * 处理音源相关的API请求
 */

const { getAvailableSources, testSourceAvailability } = require('../services/unlockService');
const { success, badRequest } = require('../utils/response');
const { asyncHandler } = require('../middleware/errorHandler');
const { logBusiness } = require('../middleware/logger');
const { MUSIC_SOURCES, SOURCE_DISPLAY_NAMES } = require('../utils/constants');
const config = require('../config/config');

/**
 * 获取所有可用音源列表
 * GET /api/sources
 */
const getSources = asyncHandler(async (req, res) => {
    const { includeStatus = false } = req.query;

    logBusiness('获取音源列表', { includeStatus });

    let sources = await getAvailableSources();

    // 如果需要包含状态信息，测试每个音源的可用性
    if (includeStatus === 'true') {
        const statusPromises = sources.map(async (source) => {
            try {
                const isAvailable = await testSourceAvailability(source.id);
                return {
                    ...source,
                    status: isAvailable ? 'online' : 'offline',
                    lastChecked: new Date().toISOString()
                };
            } catch (error) {
                return {
                    ...source,
                    status: 'error',
                    error: error.message,
                    lastChecked: new Date().toISOString()
                };
            }
        });

        sources = await Promise.all(statusPromises);
    }

    const response = {
        sources,
        total: sources.length,
        enabled: sources.filter(s => s.enabled).length,
        ...(includeStatus === 'true' && {
            online: sources.filter(s => s.status === 'online').length,
            offline: sources.filter(s => s.status === 'offline').length
        })
    };

    success(res, response, '音源列表获取成功');
});

/**
 * 获取单个音源详细信息
 * GET /api/sources/:sourceId
 */
const getSourceDetail = asyncHandler(async (req, res) => {
    const { sourceId } = req.params;

    logBusiness('获取音源详情', { sourceId });

    // 验证音源ID
    if (!Object.values(MUSIC_SOURCES).includes(sourceId)) {
        return badRequest(res, `不支持的音源: ${sourceId}`);
    }

    // 测试音源可用性
    const isAvailable = await testSourceAvailability(sourceId);

    const sourceDetail = {
        id: sourceId,
        name: SOURCE_DISPLAY_NAMES[sourceId] || sourceId,
        enabled: config.music.sources.includes(sourceId),
        priority: config.music.sources.indexOf(sourceId) + 1 || 999,
        status: isAvailable ? 'online' : 'offline',
        features: getSourceFeatures(sourceId),
        lastChecked: new Date().toISOString()
    };

    success(res, sourceDetail, '音源详情获取成功');
});

/**
 * 测试音源可用性
 * POST /api/sources/:sourceId/test
 */
const testSource = asyncHandler(async (req, res) => {
    const { sourceId } = req.params;
    const { testSongId = 418602084 } = req.body; // 默认使用周杰伦的稻香

    logBusiness('测试音源可用性', { sourceId, testSongId });

    // 验证音源ID
    if (!Object.values(MUSIC_SOURCES).includes(sourceId)) {
        return badRequest(res, `不支持的音源: ${sourceId}`);
    }

    try {
        const startTime = Date.now();
        const isAvailable = await testSourceAvailability(sourceId);
        const responseTime = Date.now() - startTime;

        const testResult = {
            sourceId,
            sourceName: SOURCE_DISPLAY_NAMES[sourceId] || sourceId,
            available: isAvailable,
            responseTime: `${responseTime}ms`,
            testSongId,
            testedAt: new Date().toISOString()
        };

        success(res, testResult, `音源 ${sourceId} 测试完成`);

    } catch (error) {
        const testResult = {
            sourceId,
            sourceName: SOURCE_DISPLAY_NAMES[sourceId] || sourceId,
            available: false,
            error: error.message,
            testSongId,
            testedAt: new Date().toISOString()
        };

        success(res, testResult, `音源 ${sourceId} 测试失败`);
    }
});

/**
 * 批量测试音源可用性
 * POST /api/sources/test/batch
 */
const testSourcesBatch = asyncHandler(async (req, res) => {
    const { sourceIds, testSongId = 418602084 } = req.body;

    logBusiness('批量测试音源', { sourceIds, testSongId });

    // 验证音源ID列表
    if (!Array.isArray(sourceIds) || sourceIds.length === 0) {
        return badRequest(res, '音源ID列表不能为空');
    }

    // 验证每个音源ID
    const invalidSources = sourceIds.filter(id => !Object.values(MUSIC_SOURCES).includes(id));
    if (invalidSources.length > 0) {
        return badRequest(res, `不支持的音源: ${invalidSources.join(', ')}`);
    }

    const testPromises = sourceIds.map(async (sourceId) => {
        const startTime = Date.now();
        try {
            const isAvailable = await testSourceAvailability(sourceId);
            const responseTime = Date.now() - startTime;

            return {
                sourceId,
                sourceName: SOURCE_DISPLAY_NAMES[sourceId] || sourceId,
                available: isAvailable,
                responseTime: `${responseTime}ms`,
                status: 'success'
            };
        } catch (error) {
            const responseTime = Date.now() - startTime;
            return {
                sourceId,
                sourceName: SOURCE_DISPLAY_NAMES[sourceId] || sourceId,
                available: false,
                responseTime: `${responseTime}ms`,
                error: error.message,
                status: 'error'
            };
        }
    });

    const results = await Promise.all(testPromises);

    const summary = {
        total: results.length,
        available: results.filter(r => r.available).length,
        unavailable: results.filter(r => !r.available).length,
        errors: results.filter(r => r.status === 'error').length,
        testSongId,
        testedAt: new Date().toISOString()
    };

    const response = {
        results,
        summary
    };

    success(res, response, `批量音源测试完成，可用: ${summary.available}/${summary.total}`);
});

/**
 * 获取音源统计信息
 * GET /api/sources/stats
 */
const getSourceStats = asyncHandler(async (req, res) => {
    logBusiness('获取音源统计');

    const sources = await getAvailableSources();

    // 这里可以添加更多统计信息，比如从数据库获取使用频率等
    const stats = {
        totalSources: sources.length,
        enabledSources: sources.filter(s => s.enabled).length,
        disabledSources: sources.filter(s => !s.enabled).length,
        sourceBreakdown: sources.map(source => ({
            id: source.id,
            name: source.name,
            enabled: source.enabled,
            priority: source.priority
        })),
        lastUpdated: new Date().toISOString()
    };

    success(res, stats, '音源统计信息获取成功');
});

/**
 * 获取音源配置信息
 * GET /api/sources/config
 */
const getSourceConfig = asyncHandler(async (req, res) => {
    logBusiness('获取音源配置');

    const sourceConfig = {
        enabledSources: config.music.sources,
        sourceOrder: config.music.sources.map((sourceId, index) => ({
            id: sourceId,
            name: SOURCE_DISPLAY_NAMES[sourceId] || sourceId,
            priority: index + 1
        })),
        settings: {
            followSourceOrder: config.music.followSourceOrder,
            enableFlac: config.music.enableFlac,
            minBitrate: config.music.minBitrate,
            enableLocalVip: config.music.enableLocalVip
        }
    };

    success(res, sourceConfig, '音源配置获取成功');
});

/**
 * 获取音源特性信息
 * @param {string} sourceId - 音源ID
 * @returns {Object} 音源特性
 */
function getSourceFeatures(sourceId) {
    const features = {
        [MUSIC_SOURCES.QQ]: {
            supportedFormats: ['mp3', 'm4a', 'flac'],
            maxBitrate: 999000,
            hasLyrics: true,
            hasAlbumArt: true,
            requiresCookie: true
        },
        [MUSIC_SOURCES.KUGOU]: {
            supportedFormats: ['mp3', 'flac'],
            maxBitrate: 999000,
            hasLyrics: true,
            hasAlbumArt: true,
            requiresCookie: false
        },
        [MUSIC_SOURCES.KUWO]: {
            supportedFormats: ['mp3'],
            maxBitrate: 320000,
            hasLyrics: true,
            hasAlbumArt: true,
            requiresCookie: false
        },
        [MUSIC_SOURCES.MIGU]: {
            supportedFormats: ['mp3', 'flac'],
            maxBitrate: 999000,
            hasLyrics: true,
            hasAlbumArt: true,
            requiresCookie: false
        },
        [MUSIC_SOURCES.JOOX]: {
            supportedFormats: ['mp3', 'm4a'],
            maxBitrate: 320000,
            hasLyrics: false,
            hasAlbumArt: true,
            requiresCookie: true
        },
        [MUSIC_SOURCES.YOUTUBE]: {
            supportedFormats: ['mp3', 'm4a'],
            maxBitrate: 192000,
            hasLyrics: false,
            hasAlbumArt: true,
            requiresCookie: false
        }
    };

    return features[sourceId] || {
        supportedFormats: ['mp3'],
        maxBitrate: 128000,
        hasLyrics: false,
        hasAlbumArt: false,
        requiresCookie: false
    };
}

module.exports = {
    getSources,
    getSourceDetail,
    testSource,
    testSourcesBatch,
    getSourceStats,
    getSourceConfig
};
