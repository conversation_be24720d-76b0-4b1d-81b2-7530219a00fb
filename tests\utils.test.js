/**
 * Utils模块单元测试
 * 测试response.js和constants.js的功能
 */

const {
    success,
    error,
    badRequest,
    notFound,
    internalError,
    serviceUnavailable,
    searchResults,
    created,
    paginated
} = require('../src/utils/response');

const { 
    HTTP_STATUS, 
    ERROR_CODES, 
    SEARCH_TYPES, 
    MUSIC_SOURCES, 
    SOURCE_DISPLAY_NAMES,
    QUALITY_LEVELS,
    API_LIMITS
} = require('../src/utils/constants');

describe('Response Utils测试', () => {
    let mockRes;

    beforeEach(() => {
        // 模拟Express响应对象
        mockRes = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockReturnThis()
        };
        
        // 重置环境变量
        delete process.env.API_RESPONSE_FORMAT;
    });

    describe('响应格式测试', () => {
        test('环境变量控制响应格式', () => {
            // 测试不同的环境变量设置
            process.env.API_RESPONSE_FORMAT = 'simple';

            // 通过success函数间接测试格式
            success(mockRes, { id: 1 }, 'Success');

            // 验证调用了正确的方法
            expect(mockRes.status).toHaveBeenCalledWith(200);
            expect(mockRes.json).toHaveBeenCalled();
        });
    });

    describe('成功响应函数', () => {
        test('success函数', () => {
            const data = { id: 1, name: 'test' };
            success(mockRes, data, 'Success message');

            expect(mockRes.status).toHaveBeenCalledWith(200);
            expect(mockRes.json).toHaveBeenCalled();

            // 验证调用的参数包含基本字段
            const callArgs = mockRes.json.mock.calls[0][0];
            expect(callArgs).toHaveProperty('code', 200);
            expect(callArgs).toHaveProperty('data', data);
            expect(callArgs).toHaveProperty('message', 'Success message');
        });

        test('searchResults函数', () => {
            const results = [{ id: 1 }, { id: 2 }];
            searchResults(mockRes, results, 'test query', 2, 100);

            expect(mockRes.status).toHaveBeenCalledWith(200);
            expect(mockRes.json).toHaveBeenCalled();

            // 验证调用的参数包含基本字段
            const callArgs = mockRes.json.mock.calls[0][0];
            expect(callArgs).toHaveProperty('code', 200);
            expect(callArgs).toHaveProperty('data');
        });
    });

    describe('错误响应函数', () => {
        test('error函数', () => {
            error(mockRes, 500, 'Error message');

            expect(mockRes.status).toHaveBeenCalledWith(500);
            expect(mockRes.json).toHaveBeenCalled();
        });

        test('error函数带详情', () => {
            error(mockRes, 500, 'Error message', 'ERR_001', { stack: 'error stack' });

            expect(mockRes.status).toHaveBeenCalledWith(500);
            expect(mockRes.json).toHaveBeenCalled();
            const response = mockRes.json.mock.calls[0][0];
            expect(response.details).toEqual({ stack: 'error stack' });
        });

        test('badRequest函数', () => {
            badRequest(mockRes, 'Bad request');

            expect(mockRes.status).toHaveBeenCalledWith(400);
            expect(mockRes.json).toHaveBeenCalled();
        });

        test('notFound函数', () => {
            notFound(mockRes, 'Not found');

            expect(mockRes.status).toHaveBeenCalledWith(404);
            expect(mockRes.json).toHaveBeenCalled();
        });

        test('internalError函数', () => {
            internalError(mockRes, 'Internal error');

            expect(mockRes.status).toHaveBeenCalledWith(500);
            expect(mockRes.json).toHaveBeenCalled();
        });

        test('serviceUnavailable函数', () => {
            serviceUnavailable(mockRes, 'Service unavailable');

            expect(mockRes.status).toHaveBeenCalledWith(503);
            expect(mockRes.json).toHaveBeenCalled();
        });
    });

    describe('created函数测试', () => {
        test('应该返回201状态码和创建成功消息', () => {
            delete process.env.API_RESPONSE_FORMAT; // 使用默认格式
            created(mockRes, { id: 1 }, '资源创建成功');

            expect(mockRes.status).toHaveBeenCalledWith(201);
            expect(mockRes.json).toHaveBeenCalledWith({
                code: 201,
                message: '资源创建成功',
                data: { id: 1 },
                timestamp: expect.any(String)
            });
        });

        test('应该使用默认创建消息', () => {
            delete process.env.API_RESPONSE_FORMAT; // 使用默认格式
            created(mockRes, { id: 1 });

            expect(mockRes.status).toHaveBeenCalledWith(201);
            expect(mockRes.json).toHaveBeenCalledWith({
                code: 201,
                message: '创建成功',
                data: { id: 1 },
                timestamp: expect.any(String)
            });
        });
    });

    describe('paginated函数测试', () => {
        test('应该返回分页响应', () => {
            delete process.env.API_RESPONSE_FORMAT; // 使用默认格式
            const data = [{ id: 1 }, { id: 2 }];
            paginated(mockRes, data, 1, 10, 25, '分页数据获取成功');

            expect(mockRes.status).toHaveBeenCalledWith(200);
            expect(mockRes.json).toHaveBeenCalledWith({
                code: 200,
                message: '分页数据获取成功',
                data: data,
                timestamp: expect.any(String),
                meta: {
                    pagination: {
                        page: 1,
                        pageSize: 10,
                        total: 25,
                        totalPages: 3,
                        hasNext: true,
                        hasPrev: false
                    }
                }
            });
        });

        test('应该正确计算最后一页', () => {
            delete process.env.API_RESPONSE_FORMAT; // 使用默认格式
            const data = [{ id: 1 }];
            paginated(mockRes, data, 3, 10, 25);

            expect(mockRes.status).toHaveBeenCalledWith(200);
            const response = mockRes.json.mock.calls[0][0];
            expect(response.meta.pagination.hasNext).toBe(false);
            expect(response.meta.pagination.hasPrev).toBe(true);
        });
    });

    describe('自定义格式测试', () => {
        test('应该返回自定义格式响应', () => {
            process.env.API_RESPONSE_FORMAT = 'custom';

            success(mockRes, { test: 'data' }, '自定义成功');

            expect(mockRes.status).toHaveBeenCalledWith(200);
            expect(mockRes.json).toHaveBeenCalledWith({
                status: 'success',
                code: 200,
                message: '自定义成功',
                data: { test: 'data' },
                timestamp: expect.any(String),
                version: expect.any(String),
                meta: null
            });
        });
    });
});

describe('Constants测试', () => {
    test('HTTP_STATUS常量', () => {
        expect(HTTP_STATUS.OK).toBe(200);
        expect(HTTP_STATUS.BAD_REQUEST).toBe(400);
        expect(HTTP_STATUS.NOT_FOUND).toBe(404);
        expect(HTTP_STATUS.INTERNAL_SERVER_ERROR).toBe(500);
        expect(HTTP_STATUS.SERVICE_UNAVAILABLE).toBe(503);
    });

    test('ERROR_CODES常量', () => {
        expect(ERROR_CODES.VALIDATION_ERROR).toBe('VALIDATION_ERROR');
        expect(ERROR_CODES.SONG_NOT_FOUND).toBe('SONG_NOT_FOUND');
        expect(ERROR_CODES.INTERNAL_ERROR).toBe('INTERNAL_ERROR');
        expect(ERROR_CODES.SOURCE_UNAVAILABLE).toBe('SOURCE_UNAVAILABLE');
    });

    test('SEARCH_TYPES常量', () => {
        expect(SEARCH_TYPES.ID).toBe('id');
        expect(SEARCH_TYPES.KEYWORD).toBe('keyword');
        expect(SEARCH_TYPES.BATCH).toBe('batch');
    });

    test('MUSIC_SOURCES常量', () => {
        expect(typeof MUSIC_SOURCES).toBe('object');
        expect(MUSIC_SOURCES.QQ).toBe('qq');
        expect(MUSIC_SOURCES.KUGOU).toBe('kugou');
        expect(MUSIC_SOURCES.KUWO).toBe('kuwo');
        expect(MUSIC_SOURCES.MIGU).toBe('migu');
    });

    test('SOURCE_DISPLAY_NAMES常量', () => {
        expect(typeof SOURCE_DISPLAY_NAMES).toBe('object');
        expect(SOURCE_DISPLAY_NAMES.qq).toBe('QQ音乐');
        expect(SOURCE_DISPLAY_NAMES.kugou).toBe('酷狗音乐');
    });

    test('QUALITY_LEVELS常量', () => {
        expect(typeof QUALITY_LEVELS).toBe('object');
        expect(QUALITY_LEVELS.LOW).toBe(128000);
        expect(QUALITY_LEVELS.STANDARD).toBe(192000);
        expect(QUALITY_LEVELS.HIGH).toBe(320000);
        expect(QUALITY_LEVELS.LOSSLESS).toBe(999000);
    });

    test('API_LIMITS常量', () => {
        expect(typeof API_LIMITS).toBe('object');
        expect(typeof API_LIMITS.MAX_BATCH_SIZE).toBe('number');
        expect(typeof API_LIMITS.MAX_SEARCH_RESULTS).toBe('number');
        expect(API_LIMITS.MAX_BATCH_SIZE).toBeGreaterThan(0);
        expect(API_LIMITS.MAX_SEARCH_RESULTS).toBeGreaterThan(0);
    });
});
