/* 音乐解锁服务 - 样式文件 */

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    margin-top: 20px;
    margin-bottom: 20px;
}

/* 页面头部 */
.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #f0f0f0;
}

.header h1 {
    font-size: 2.5em;
    color: #4a5568;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
    font-size: 1.1em;
    color: #718096;
    margin-bottom: 15px;
}

.status-bar {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.status-indicator {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 500;
}

.status-indicator.online {
    background: #48bb78;
    color: white;
}

.status-indicator.offline {
    background: #f56565;
    color: white;
}

.status-indicator.checking {
    background: #ed8936;
    color: white;
}

.version-info {
    background: #4299e1;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9em;
}

/* 导航标签 */
.nav-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    border-bottom: 1px solid #e2e8f0;
    flex-wrap: wrap;
    gap: 5px;
}

.tab-btn {
    padding: 12px 24px;
    border: none;
    background: transparent;
    color: #718096;
    cursor: pointer;
    font-size: 1em;
    font-weight: 500;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    position: relative;
}

.tab-btn:hover {
    background: #f7fafc;
    color: #4a5568;
}

.tab-btn.active {
    background: #4299e1;
    color: white;
    box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
}

/* 内容区域 */
.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.section h2 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.5em;
    border-left: 4px solid #4299e1;
    padding-left: 15px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #4a5568;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1em;
    transition: border-color 0.3s ease;
    background: #f7fafc;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4299e1;
    background: white;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: #718096;
    font-size: 0.9em;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

/* 按钮样式 */
.button-group {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: #4299e1;
    color: white;
}

.btn-primary:hover {
    background: #3182ce;
}

.btn-secondary {
    background: #718096;
    color: white;
}

.btn-secondary:hover {
    background: #4a5568;
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover {
    background: #38a169;
}

.btn-warning {
    background: #ed8936;
    color: white;
}

.btn-warning:hover {
    background: #dd6b20;
}

.btn-info {
    background: #38b2ac;
    color: white;
}

.btn-info:hover {
    background: #319795;
}

.btn-danger {
    background: #f56565;
    color: white;
}

.btn-danger:hover {
    background: #e53e3e;
}

/* 结果显示区域 */
.result-container {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    max-height: 500px;
    overflow-y: auto;
}

.result-container:empty {
    display: none;
}

.result-container pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
    font-size: 0.9em;
    line-height: 1.4;
}

.result-container .success {
    color: #38a169;
    font-weight: 500;
}

.result-container .error {
    color: #e53e3e;
    font-weight: 500;
}

.result-container .info {
    color: #3182ce;
    font-weight: 500;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 页面底部 */
.footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px 0;
    border-top: 1px solid #e2e8f0;
    color: #718096;
    font-size: 0.9em;
}

.footer a {
    color: #4299e1;
    text-decoration: none;
}

.footer a:hover {
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
        border-radius: 10px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .nav-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        border-radius: 8px;
        margin-bottom: 5px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 10px;
    }
}

/* 特殊样式 */
.highlight {
    background: #fef5e7;
    border-left: 4px solid #ed8936;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
}

.card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.badge {
    display: inline-block;
    padding: 4px 8px;
    background: #4299e1;
    color: white;
    border-radius: 12px;
    font-size: 0.8em;
    margin: 2px;
}
