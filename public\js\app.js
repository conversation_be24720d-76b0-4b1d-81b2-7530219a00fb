/**
 * 音乐解锁服务前端JavaScript代码
 * 实现所有的前端交互功能
 */

// 全局配置
const API_BASE = '/api';
const HEALTH_CHECK_URL = '/health';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('音乐解锁服务前端已加载');
    
    // 初始化页面
    initializePage();
    
    // 检查服务状态
    checkServiceStatus();
    
    // 绑定标签切换事件
    bindTabEvents();
    
    // 定期检查服务状态
    setInterval(checkServiceStatus, 30000); // 每30秒检查一次
});

/**
 * 初始化页面
 */
function initializePage() {
    // 设置默认值
    document.getElementById('song-id').value = '418602084';
    document.getElementById('search-query').value = '周杰伦';
    document.getElementById('unlock-ids').value = '418602084\n186016\n185868';
    
    // 绑定搜索类型切换事件
    document.getElementById('search-type').addEventListener('change', toggleSearchInputs);
    
    // 初始化搜索输入提示
    toggleSearchInputs();
}

/**
 * 检查服务状态
 */
async function checkServiceStatus() {
    const statusElement = document.getElementById('service-status');
    const versionElement = document.getElementById('api-version');
    
    try {
        statusElement.textContent = '检查中...';
        statusElement.className = 'status-indicator checking';
        
        // 检查健康状态
        const healthResponse = await fetch(HEALTH_CHECK_URL);
        const healthData = await healthResponse.json();
        
        // 获取API信息
        const apiResponse = await fetch(API_BASE);
        const apiData = await apiResponse.json();
        
        if (healthResponse.ok && healthData.data.status === 'healthy') {
            statusElement.textContent = '🟢 服务正常';
            statusElement.className = 'status-indicator online';
            versionElement.textContent = `v${apiData.version}`;
        } else {
            throw new Error('服务异常');
        }
    } catch (error) {
        statusElement.textContent = '🔴 服务异常';
        statusElement.className = 'status-indicator offline';
        versionElement.textContent = '无法连接';
        console.error('服务状态检查失败:', error);
    }
}

/**
 * 绑定标签切换事件
 */
function bindTabEvents() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // 移除所有活动状态
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 激活当前标签
            this.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.add('active');
        });
    });
}

/**
 * 切换搜索输入提示
 */
function toggleSearchInputs() {
    const searchType = document.getElementById('search-type').value;
    const queryInput = document.getElementById('search-query');
    const hintElement = document.getElementById('search-hint');
    const paginationElement = document.getElementById('search-pagination');
    
    switch (searchType) {
        case 'keyword':
            queryInput.placeholder = '输入歌手名、歌曲名或专辑名';
            hintElement.textContent = '输入歌手名、歌曲名或专辑名进行搜索';
            paginationElement.style.display = 'block';
            break;
        case 'id':
            queryInput.placeholder = '输入歌曲ID，例如: 418602084';
            hintElement.textContent = '输入网易云音乐歌曲ID';
            paginationElement.style.display = 'none';
            break;
        case 'batch':
            queryInput.placeholder = '输入多个歌曲ID，用逗号分隔';
            hintElement.textContent = '输入多个歌曲ID，用逗号分隔，例如: 418602084,186016';
            paginationElement.style.display = 'none';
            break;
    }
}

/**
 * 显示结果
 */
function showResult(containerId, data, type = 'info') {
    const container = document.getElementById(containerId);
    container.innerHTML = '';
    
    if (typeof data === 'string') {
        container.innerHTML = `<div class="${type}">${data}</div>`;
    } else {
        container.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
    }
    
    container.scrollTop = 0;
}

/**
 * 显示加载状态
 */
function showLoading(containerId, message = '处理中...') {
    const container = document.getElementById(containerId);
    container.innerHTML = `<div class="loading"></div>${message}`;
}

/**
 * API请求封装
 */
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(API_BASE + url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || `HTTP ${response.status}`);
        }
        
        return data;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

// ==================== 歌曲信息功能 ====================

/**
 * 获取歌曲信息
 */
async function getSongInfo() {
    const songId = document.getElementById('song-id').value.trim();
    const sources = document.getElementById('song-sources').value.trim();
    
    if (!songId) {
        showResult('song-result', '请输入歌曲ID', 'error');
        return;
    }
    
    showLoading('song-result', '正在获取歌曲信息...');
    
    try {
        const url = `/song/${songId}${sources ? `?sources=${sources}` : ''}`;
        const result = await apiRequest(url);
        showResult('song-result', result);
    } catch (error) {
        showResult('song-result', `获取失败: ${error.message}`, 'error');
    }
}

/**
 * 获取歌曲元数据
 */
async function getSongMetadata() {
    const songId = document.getElementById('song-id').value.trim();
    
    if (!songId) {
        showResult('song-result', '请输入歌曲ID', 'error');
        return;
    }
    
    showLoading('song-result', '正在获取歌曲元数据...');
    
    try {
        const result = await apiRequest(`/metadata/${songId}`);
        showResult('song-result', result);
    } catch (error) {
        showResult('song-result', `获取失败: ${error.message}`, 'error');
    }
}

/**
 * 检查歌曲可用性
 */
async function checkSongAvailability() {
    const songId = document.getElementById('song-id').value.trim();
    const sources = document.getElementById('song-sources').value.trim();
    
    if (!songId) {
        showResult('song-result', '请输入歌曲ID', 'error');
        return;
    }
    
    showLoading('song-result', '正在检查歌曲可用性...');
    
    try {
        const url = `/song/${songId}${sources ? `?sources=${sources}` : ''}`;
        const response = await fetch(API_BASE + url, { method: 'HEAD' });
        
        if (response.ok) {
            showResult('song-result', '✅ 歌曲可用', 'success');
        } else {
            showResult('song-result', '❌ 歌曲不可用', 'error');
        }
    } catch (error) {
        showResult('song-result', `检查失败: ${error.message}`, 'error');
    }
}

// ==================== 搜索功能 ====================

/**
 * 执行搜索
 */
async function performSearch() {
    const searchType = document.getElementById('search-type').value;
    const query = document.getElementById('search-query').value.trim();
    const sources = document.getElementById('search-sources').value.trim();
    
    if (!query) {
        showResult('search-result', '请输入搜索内容', 'error');
        return;
    }
    
    showLoading('search-result', '正在搜索...');
    
    try {
        let result;
        
        switch (searchType) {
            case 'keyword':
                result = await searchByKeyword(query, sources);
                break;
            case 'id':
                result = await searchById(query, sources);
                break;
            case 'batch':
                result = await searchBatch(query, sources);
                break;
        }
        
        showResult('search-result', result);
    } catch (error) {
        showResult('search-result', `搜索失败: ${error.message}`, 'error');
    }
}

/**
 * 关键词搜索
 */
async function searchByKeyword(keyword, sources) {
    const page = document.getElementById('search-page').value || 1;
    const pageSize = document.getElementById('search-pagesize').value || 20;
    
    const params = new URLSearchParams({
        q: keyword,
        page,
        pageSize
    });
    
    if (sources) {
        params.append('sources', sources);
    }
    
    return await apiRequest(`/search/keyword?${params}`);
}

/**
 * ID搜索
 */
async function searchById(songId, sources) {
    const params = sources ? `?sources=${sources}` : '';
    return await apiRequest(`/search/id/${songId}${params}`);
}

/**
 * 批量搜索
 */
async function searchBatch(queries, sources) {
    const queryList = queries.split(',').map(q => q.trim()).filter(q => q);
    
    return await apiRequest('/search/batch', {
        method: 'POST',
        body: JSON.stringify({
            queries: queryList,
            sources: sources ? sources.split(',').map(s => s.trim()) : null
        })
    });
}

/**
 * 获取搜索建议
 */
async function getSearchSuggestions() {
    const keyword = document.getElementById('search-query').value.trim();
    
    if (!keyword) {
        showResult('search-result', '请输入关键词', 'error');
        return;
    }
    
    showLoading('search-result', '正在获取搜索建议...');
    
    try {
        const result = await apiRequest(`/search/suggest?q=${encodeURIComponent(keyword)}`);
        showResult('search-result', result);
    } catch (error) {
        showResult('search-result', `获取失败: ${error.message}`, 'error');
    }
}

/**
 * 获取热门搜索
 */
async function getTrendingSearches() {
    showLoading('search-result', '正在获取热门搜索...');

    try {
        const result = await apiRequest('/search/trending');
        showResult('search-result', result);
    } catch (error) {
        showResult('search-result', `获取失败: ${error.message}`, 'error');
    }
}

// ==================== 解锁功能 ====================

/**
 * 批量解锁歌曲
 */
async function unlockSongs() {
    const idsText = document.getElementById('unlock-ids').value.trim();
    const sources = document.getElementById('unlock-sources').value.trim();
    const minBitrate = parseInt(document.getElementById('min-bitrate').value);
    const detailed = document.getElementById('detailed-result').checked;

    if (!idsText) {
        showResult('unlock-result', '请输入歌曲ID', 'error');
        return;
    }

    // 解析歌曲ID列表
    const songIds = parseIdList(idsText);
    if (songIds.length === 0) {
        showResult('unlock-result', '请输入有效的歌曲ID', 'error');
        return;
    }

    showLoading('unlock-result', `正在解锁 ${songIds.length} 首歌曲...`);

    try {
        const requestBody = {
            songIds,
            minBitrate,
            detailed
        };

        if (sources) {
            requestBody.sources = sources.split(',').map(s => s.trim());
        }

        const result = await apiRequest('/unlock', {
            method: 'POST',
            body: JSON.stringify(requestBody)
        });

        showResult('unlock-result', result);
    } catch (error) {
        showResult('unlock-result', `解锁失败: ${error.message}`, 'error');
    }
}

/**
 * 快速解锁
 */
async function quickUnlock() {
    const idsText = document.getElementById('unlock-ids').value.trim();
    const sources = document.getElementById('unlock-sources').value.trim();

    if (!idsText) {
        showResult('unlock-result', '请输入歌曲ID', 'error');
        return;
    }

    const songIds = parseIdList(idsText);
    if (songIds.length === 0) {
        showResult('unlock-result', '请输入有效的歌曲ID', 'error');
        return;
    }

    showLoading('unlock-result', `正在快速解锁 ${songIds.length} 首歌曲...`);

    try {
        const requestBody = { songIds };

        if (sources) {
            requestBody.sources = sources.split(',').map(s => s.trim());
        }

        const result = await apiRequest('/unlock/quick', {
            method: 'POST',
            body: JSON.stringify(requestBody)
        });

        showResult('unlock-result', result);
    } catch (error) {
        showResult('unlock-result', `快速解锁失败: ${error.message}`, 'error');
    }
}

/**
 * 检查解锁状态
 */
async function checkUnlockStatus() {
    const idsText = document.getElementById('unlock-ids').value.trim();
    const sources = document.getElementById('unlock-sources').value.trim();

    if (!idsText) {
        showResult('unlock-result', '请输入歌曲ID', 'error');
        return;
    }

    const songIds = parseIdList(idsText);

    if (songIds.length === 1) {
        // 单首歌曲状态检查
        showLoading('unlock-result', '正在检查解锁状态...');

        try {
            const params = sources ? `?sources=${sources}` : '';
            const result = await apiRequest(`/unlock/status/${songIds[0]}${params}`);
            showResult('unlock-result', result);
        } catch (error) {
            showResult('unlock-result', `状态检查失败: ${error.message}`, 'error');
        }
    } else if (songIds.length > 1) {
        // 批量状态检查
        showLoading('unlock-result', `正在检查 ${songIds.length} 首歌曲状态...`);

        try {
            const requestBody = { songIds };

            if (sources) {
                requestBody.sources = sources.split(',').map(s => s.trim());
            }

            const result = await apiRequest('/unlock/status/batch', {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            showResult('unlock-result', result);
        } catch (error) {
            showResult('unlock-result', `批量状态检查失败: ${error.message}`, 'error');
        }
    } else {
        showResult('unlock-result', '请输入有效的歌曲ID', 'error');
    }
}

/**
 * 解析ID列表
 */
function parseIdList(idsText) {
    return idsText
        .split(/[,\n\r\s]+/)
        .map(id => id.trim())
        .filter(id => id && /^\d+$/.test(id))
        .map(id => parseInt(id));
}

// ==================== 音源管理功能 ====================

/**
 * 获取音源列表
 */
async function getSources() {
    showLoading('sources-result', '正在获取音源列表...');

    try {
        const result = await apiRequest('/sources');
        showResult('sources-result', result);
    } catch (error) {
        showResult('sources-result', `获取失败: ${error.message}`, 'error');
    }
}

/**
 * 获取音源列表（包含状态）
 */
async function getSourcesWithStatus() {
    showLoading('sources-result', '正在获取音源状态...');

    try {
        const result = await apiRequest('/sources?includeStatus=true');
        showResult('sources-result', result);
    } catch (error) {
        showResult('sources-result', `获取失败: ${error.message}`, 'error');
    }
}

/**
 * 测试所有音源
 */
async function testAllSources() {
    showLoading('sources-result', '正在测试所有音源...');

    try {
        const sourceIds = ['qq', 'kugou', 'kuwo', 'migu', 'joox', 'youtube'];
        const result = await apiRequest('/sources/test/batch', {
            method: 'POST',
            body: JSON.stringify({ sourceIds })
        });
        showResult('sources-result', result);
    } catch (error) {
        showResult('sources-result', `测试失败: ${error.message}`, 'error');
    }
}

/**
 * 测试单个音源
 */
async function testSingleSource() {
    const sourceId = document.getElementById('test-source-id').value;

    showLoading('sources-result', `正在测试 ${sourceId} 音源...`);

    try {
        const result = await apiRequest(`/sources/${sourceId}/test`, {
            method: 'POST',
            body: JSON.stringify({})
        });
        showResult('sources-result', result);
    } catch (error) {
        showResult('sources-result', `测试失败: ${error.message}`, 'error');
    }
}

/**
 * 获取音源统计
 */
async function getSourceStats() {
    showLoading('sources-result', '正在获取音源统计...');

    try {
        const result = await apiRequest('/sources/stats');
        showResult('sources-result', result);
    } catch (error) {
        showResult('sources-result', `获取失败: ${error.message}`, 'error');
    }
}

// ==================== API文档功能 ====================

/**
 * 加载API文档
 */
async function loadApiDocs() {
    showLoading('docs-result', '正在加载API文档...');

    try {
        const result = await apiRequest('/docs');
        showResult('docs-result', result);
    } catch (error) {
        showResult('docs-result', `加载失败: ${error.message}`, 'error');
    }
}
