/**
 * 参数验证中间件模块
 * 基于Joi实现的请求参数验证系统
 */

const Joi = require('joi');
const { badRequest } = require('../utils/response');
const { API_LIMITS } = require('../utils/constants');

/**
 * 创建验证中间件
 * @param {Object} schema - Joi验证模式
 * @param {string} source - 验证数据源 ('body', 'query', 'params')
 * @returns {Function} Express中间件函数
 */
function createValidator(schema, source = 'body') {
    return (req, res, next) => {
        const data = req[source];
        const { error, value } = schema.validate(data, {
            abortEarly: false, // 返回所有验证错误
            stripUnknown: true // 移除未知字段
        });

        if (error) {
            const details = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message,
                value: detail.context.value
            }));

            return badRequest(res, '请求参数验证失败', details);
        }

        // 将验证后的数据替换原始数据
        req[source] = value;
        next();
    };
}

// 通用验证模式
const commonSchemas = {
    // 歌曲ID验证
    songId: Joi.alternatives().try(
        Joi.number().integer().positive(),
        Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
    ).required().messages({
        'any.required': '歌曲ID是必需的',
        'number.positive': '歌曲ID必须是正整数',
        'string.pattern.base': '歌曲ID格式不正确'
    }),

    // 搜索关键词验证
    keyword: Joi.string()
        .trim()
        .min(1)
        .max(API_LIMITS.MAX_KEYWORD_LENGTH)
        .required()
        .messages({
            'string.empty': '搜索关键词不能为空',
            'string.min': '搜索关键词至少包含1个字符',
            'string.max': `搜索关键词不能超过${API_LIMITS.MAX_KEYWORD_LENGTH}个字符`,
            'any.required': '搜索关键词是必需的'
        }),

    // 音源列表验证
    sources: Joi.array()
        .items(Joi.string().valid('qq', 'kugou', 'kuwo', 'migu', 'joox', 'youtube', 'ytdlp', 'bilibili'))
        .min(1)
        .unique()
        .messages({
            'array.min': '至少需要指定一个音源',
            'array.unique': '音源列表不能包含重复项',
            'any.only': '不支持的音源类型'
        }),

    // 分页参数验证
    pagination: {
        page: Joi.number().integer().min(1).default(1),
        pageSize: Joi.number().integer().min(1).max(100).default(20)
    }
};

// 具体接口的验证模式
const schemas = {
    // 获取歌曲信息 - 路径参数验证
    getSong: {
        params: Joi.object({
            id: commonSchemas.songId
        })
    },

    // 搜索歌曲 - 请求体验证
    searchSongs: {
        body: Joi.object({
            // 搜索类型：id, keyword, batch
            type: Joi.string().valid('id', 'keyword', 'batch').required(),
      
            // 搜索查询内容
            query: Joi.alternatives()
                .conditional('type', {
                    is: 'id',
                    then: commonSchemas.songId,
                    otherwise: Joi.alternatives()
                        .conditional('type', {
                            is: 'keyword',
                            then: commonSchemas.keyword,
                            otherwise: Joi.array()
                                .items(Joi.alternatives().try(commonSchemas.songId, commonSchemas.keyword))
                                .min(1)
                                .max(API_LIMITS.MAX_BATCH_SIZE)
                                .required()
                        })
                }),

            // 指定音源（可选）
            sources: commonSchemas.sources.optional(),

            // 分页参数（仅对keyword和batch搜索有效）
            page: commonSchemas.pagination.page.when('type', {
                is: Joi.string().valid('keyword', 'batch'),
                then: Joi.optional(),
                otherwise: Joi.forbidden()
            }),

            pageSize: commonSchemas.pagination.pageSize.when('type', {
                is: Joi.string().valid('keyword', 'batch'),
                then: Joi.optional(),
                otherwise: Joi.forbidden()
            }),

            // 是否包含专辑信息
            includeAlbum: Joi.boolean().default(false)
        })
    },

    // 批量解锁歌曲 - 请求体验证
    unlockSongs: {
        body: Joi.object({
            // 歌曲ID列表
            songIds: Joi.array()
                .items(commonSchemas.songId)
                .min(1)
                .max(API_LIMITS.MAX_BATCH_SIZE)
                .unique()
                .required(),

            // 指定音源（可选）
            sources: commonSchemas.sources.optional(),

            // 最低音质要求
            minBitrate: Joi.number()
                .integer()
                .valid(128000, 192000, 320000, 999000)
                .default(128000),

            // 是否返回详细信息
            detailed: Joi.boolean().default(false)
        })
    },

    // 获取音源列表 - 查询参数验证
    getSources: {
        query: Joi.object({
            // 是否包含状态信息
            includeStatus: Joi.boolean().default(false)
        })
    }
};

// 导出验证中间件
const validators = {
    // 获取歌曲信息验证
    validateGetSong: createValidator(schemas.getSong.params, 'params'),

    // 搜索歌曲验证
    validateSearchSongs: createValidator(schemas.searchSongs.body, 'body'),

    // 批量解锁验证
    validateUnlockSongs: createValidator(schemas.unlockSongs.body, 'body'),

    // 获取音源列表验证
    validateGetSources: createValidator(schemas.getSources.query, 'query')
};

module.exports = {
    createValidator,
    commonSchemas,
    schemas,
    validators
};
