/**
 * 歌曲信息控制器
 * 处理歌曲相关的API请求
 */

const { getSongInfo, getSongMetadata, isValidSongId } = require('../services/musicService');
const { unlockSong } = require('../services/unlockService');
const { success, notFound, badRequest } = require('../utils/response');
const { asyncHandler } = require('../middleware/errorHandler');
const { logBusiness } = require('../middleware/logger');

/**
 * 根据ID获取歌曲信息和解锁链接
 * GET /api/song/:id
 */
const getSong = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { sources } = req.query;

    logBusiness('获取歌曲信息请求', { songId: id, sources });

    // 验证歌曲ID
    if (!isValidSongId(id)) {
        return badRequest(res, '无效的歌曲ID格式');
    }

    // 解析音源参数
    const sourcesArray = sources ? sources.split(',').map(s => s.trim()) : null;

    try {
    // 获取歌曲完整信息（包含解锁链接）
        const songInfo = await getSongInfo(id, sourcesArray);

        success(res, songInfo, '歌曲信息获取成功');

    } catch (error) {
        if (error.statusCode === 404) {
            return notFound(res, `歌曲 ${id} 未找到或无法解锁`);
        }
    
        throw error; // 让全局错误处理器处理
    }
});

/**
 * 根据ID获取歌曲元数据（不包含解锁链接）
 * GET /api/metadata/:id
 */
const getSongMetadataOnly = asyncHandler(async (req, res) => {
    const { id } = req.params;

    logBusiness('获取歌曲元数据请求', { songId: id });

    // 验证歌曲ID
    if (!isValidSongId(id)) {
        return badRequest(res, '无效的歌曲ID格式');
    }

    try {
    // 获取歌曲元数据
        const metadata = await getSongMetadata(id);

        success(res, metadata, '歌曲元数据获取成功');

    } catch (error) {
        if (error.statusCode === 404) {
            return notFound(res, `歌曲 ${id} 的元数据未找到`);
        }
    
        throw error;
    }
});

/**
 * 解锁歌曲（仅返回播放链接）
 * POST /api/song/:id/unlock
 */
const unlockSongOnly = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { sources, minBitrate } = req.body;

    logBusiness('解锁歌曲请求', { songId: id, sources, minBitrate });

    // 验证歌曲ID
    if (!isValidSongId(id)) {
        return badRequest(res, '无效的歌曲ID格式');
    }

    try {
    // 解锁歌曲
        const unlockResult = await unlockSong(id, sources);

        // 检查音质要求
        if (minBitrate && unlockResult.bitrate < minBitrate) {
            return badRequest(res, `音质不满足要求，当前: ${unlockResult.bitrate}kbps, 要求: ${minBitrate}kbps`);
        }

        // 只返回关键的解锁信息
        const result = {
            songId: unlockResult.songId,
            url: unlockResult.url,
            source: unlockResult.source,
            sourceName: unlockResult.sourceName,
            bitrate: unlockResult.bitrate,
            type: unlockResult.type,
            size: unlockResult.size,
            timestamp: unlockResult.timestamp
        };

        success(res, result, '歌曲解锁成功');

    } catch (error) {
        if (error.statusCode === 404) {
            return notFound(res, `歌曲 ${id} 无法解锁`);
        }
    
        throw error;
    }
});

/**
 * 检查歌曲是否可用
 * HEAD /api/song/:id
 */
const checkSongAvailability = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { sources } = req.query;

    logBusiness('检查歌曲可用性', { songId: id, sources });

    // 验证歌曲ID
    if (!isValidSongId(id)) {
        return badRequest(res, '无效的歌曲ID格式');
    }

    const sourcesArray = sources ? sources.split(',').map(s => s.trim()) : null;

    try {
    // 尝试解锁歌曲来检查可用性
        await unlockSong(id, sourcesArray);
    
        // 如果成功，返回200状态码（HEAD请求不返回body）
        res.status(200).end();

    } catch (error) {
        if (error.statusCode === 404) {
            // 歌曲不可用，返回404
            res.status(404).end();
        } else {
            // 其他错误，返回503
            res.status(503).end();
        }
    }
});

/**
 * 获取歌曲的所有可用音源
 * GET /api/song/:id/sources
 */
const getSongSources = asyncHandler(async (req, res) => {
    const { id } = req.params;

    logBusiness('获取歌曲音源列表', { songId: id });

    // 验证歌曲ID
    if (!isValidSongId(id)) {
        return badRequest(res, '无效的歌曲ID格式');
    }

    // 这里应该实现检查各个音源的可用性
    // 由于UnblockNeteaseMusic没有直接的API，我们需要逐个尝试
    const { MUSIC_SOURCES, SOURCE_DISPLAY_NAMES } = require('../utils/constants');
    const availableSources = [];

    // 逐个测试音源（这可能比较耗时，实际项目中可以考虑缓存）
    for (const source of Object.values(MUSIC_SOURCES)) {
        try {
            await unlockSong(id, [source]);
            availableSources.push({
                id: source,
                name: SOURCE_DISPLAY_NAMES[source] || source,
                available: true
            });
        } catch (error) {
            // 音源不可用，跳过
            availableSources.push({
                id: source,
                name: SOURCE_DISPLAY_NAMES[source] || source,
                available: false
            });
        }
    }

    const result = {
        songId: parseInt(id),
        sources: availableSources,
        availableCount: availableSources.filter(s => s.available).length,
        totalCount: availableSources.length
    };

    success(res, result, '歌曲音源列表获取成功');
});

/**
 * 获取歌曲详细信息（包含所有可用信息）
 * GET /api/song/:id/detail
 */
const getSongDetail = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { sources, includeSources = false } = req.query;

    logBusiness('获取歌曲详细信息', { songId: id, sources, includeSources });

    // 验证歌曲ID
    if (!isValidSongId(id)) {
        return badRequest(res, '无效的歌曲ID格式');
    }

    const sourcesArray = sources ? sources.split(',').map(s => s.trim()) : null;

    try {
    // 获取基本信息
        const songInfo = await getSongInfo(id, sourcesArray);
    
        let result = songInfo;

        // 如果需要包含音源信息
        if (includeSources === 'true') {
            const sourcesInfo = await getSongSources(req, res);
            result.availableSources = sourcesInfo.data.sources;
        }

        success(res, result, '歌曲详细信息获取成功');

    } catch (error) {
        if (error.statusCode === 404) {
            return notFound(res, `歌曲 ${id} 详细信息未找到`);
        }
    
        throw error;
    }
});

module.exports = {
    getSong,
    getSongMetadataOnly,
    unlockSongOnly,
    checkSongAvailability,
    getSongSources,
    getSongDetail
};
