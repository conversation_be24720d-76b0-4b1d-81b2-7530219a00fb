/**
 * 解锁相关路由
 */

const express = require('express');
const router = express.Router();

// 导入控制器
const {
    unlockSongs,
    unlockSingleSong,
    quickUnlock,
    checkUnlockStatus,
    checkBatchUnlockStatus
} = require('../controllers/unlockController');

// 导入验证中间件
const { validators } = require('../middleware/validator');

// 批量解锁歌曲
router.post('/', validators.validateUnlockSongs, unlockSongs);

// 单首歌曲解锁
router.post('/:id', unlockSingleSong);

// 快速解锁（仅返回播放URL）
router.post('/quick', quickUnlock);

// 检查解锁状态
router.get('/status/:id', checkUnlockStatus);

// 批量检查解锁状态
router.post('/status/batch', checkBatchUnlockStatus);

module.exports = router;
