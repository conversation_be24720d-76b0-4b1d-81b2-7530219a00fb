/**
 * 音乐搜索服务模块
 * 提供ID搜索、关键词搜索、批量搜索等功能
 */

const { getSongInfo, getSongMetadata, isValidSongId } = require('./musicService');
// const { unlockSong } = require('./unlockService');
const { logError, logPerformance, logBusiness } = require('../middleware/logger');
const { NotFoundError, ValidationError, ServiceUnavailableError } = require('../middleware/errorHandler');
const { SEARCH_TYPES, API_LIMITS } = require('../utils/constants');

/**
 * 根据歌曲ID精确搜索
 * @param {number|string} songId - 歌曲ID
 * @param {Array<string>} sources - 指定音源
 * @param {Object} options - 搜索选项
 * @returns {Promise<Object>} 搜索结果
 */
async function searchById(songId, sources = null, options = {}) {
    const startTime = Date.now();
    const { includeUnlockUrl = true } = options;

    try {
        logBusiness('ID搜索开始', { songId, sources, options });

        // 验证歌曲ID
        if (!isValidSongId(songId)) {
            throw new ValidationError(`无效的歌曲ID: ${songId}`);
        }

        // 获取歌曲信息
        const songInfo = includeUnlockUrl ? 
            await getSongInfo(songId, sources) : 
            await getSongMetadata(songId);

        const result = {
            type: SEARCH_TYPES.ID,
            query: songId,
            results: [songInfo],
            total: 1,
            took: Date.now() - startTime
        };

        logBusiness('ID搜索成功', { songId, title: songInfo.title });
        logPerformance('ID搜索', result.took, { songId, success: true });

        return result;

    } catch (error) {
        const duration = Date.now() - startTime;
        logPerformance('ID搜索失败', duration, { songId, error: error.message });
    
        if (error instanceof ValidationError || error instanceof NotFoundError) {
            throw error;
        }
    
        logError(error, { context: 'search_by_id', songId });
        throw new ServiceUnavailableError(`搜索歌曲 ${songId} 失败: ${error.message}`);
    }
}

/**
 * 根据关键词模糊搜索
 * @param {string} keyword - 搜索关键词
 * @param {Array<string>} sources - 指定音源
 * @param {Object} options - 搜索选项
 * @returns {Promise<Object>} 搜索结果
 */
async function searchByKeyword(keyword, sources = null, options = {}) {
    const startTime = Date.now();
    const { 
        page = 1, 
        pageSize = 20, 
        includeAlbum = false,
        includeUnlockUrl = false 
    } = options;

    try {
        logBusiness('关键词搜索开始', { keyword, sources, options });

        // 验证关键词
        if (!keyword || keyword.trim().length === 0) {
            throw new ValidationError('搜索关键词不能为空');
        }

        if (keyword.length > API_LIMITS.MAX_KEYWORD_LENGTH) {
            throw new ValidationError(`搜索关键词过长，最大长度: ${API_LIMITS.MAX_KEYWORD_LENGTH}`);
        }

        // 执行搜索
        const searchResults = await performKeywordSearch(keyword, sources, {
            page,
            pageSize,
            includeAlbum,
            includeUnlockUrl
        });

        const result = {
            type: SEARCH_TYPES.KEYWORD,
            query: keyword,
            results: searchResults.items,
            total: searchResults.total,
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            took: Date.now() - startTime
        };

        logBusiness('关键词搜索完成', { 
            keyword, 
            total: result.total, 
            returned: result.results.length 
        });
        logPerformance('关键词搜索', result.took, { 
            keyword, 
            total: result.total,
            success: true 
        });

        return result;

    } catch (error) {
        const duration = Date.now() - startTime;
        logPerformance('关键词搜索失败', duration, { keyword, error: error.message });
    
        if (error instanceof ValidationError) {
            throw error;
        }
    
        logError(error, { context: 'search_by_keyword', keyword });
        throw new ServiceUnavailableError(`搜索关键词 "${keyword}" 失败: ${error.message}`);
    }
}

/**
 * 批量搜索
 * @param {Array} queries - 搜索查询列表（可包含ID和关键词）
 * @param {Array<string>} sources - 指定音源
 * @param {Object} options - 搜索选项
 * @returns {Promise<Object>} 批量搜索结果
 */
async function searchBatch(queries, sources = null, options = {}) {
    const startTime = Date.now();
    const { includeUnlockUrl = false } = options;

    try {
        logBusiness('批量搜索开始', { count: queries.length, sources, options });

        // 验证查询列表
        if (!Array.isArray(queries) || queries.length === 0) {
            throw new ValidationError('查询列表不能为空');
        }

        if (queries.length > API_LIMITS.MAX_BATCH_SIZE) {
            throw new ValidationError(`批量搜索数量超限，最大: ${API_LIMITS.MAX_BATCH_SIZE}`);
        }

        const results = {
            type: SEARCH_TYPES.BATCH,
            queries: queries,
            results: [],
            success: [],
            failed: [],
            total: queries.length,
            successCount: 0,
            failedCount: 0,
            took: 0
        };

        // 并发处理搜索请求
        const concurrency = 3;
        const chunks = [];
        for (let i = 0; i < queries.length; i += concurrency) {
            chunks.push(queries.slice(i, i + concurrency));
        }

        for (const chunk of chunks) {
            const promises = chunk.map(async (query) => {
                try {
                    let searchResult;
          
                    // 判断是ID搜索还是关键词搜索
                    if (isValidSongId(query)) {
                        searchResult = await searchById(query, sources, { includeUnlockUrl });
                    } else {
                        searchResult = await searchByKeyword(query, sources, { 
                            page: 1, 
                            pageSize: 10, 
                            includeUnlockUrl 
                        });
                    }

                    const successItem = {
                        query,
                        type: searchResult.type,
                        results: searchResult.results,
                        total: searchResult.total
                    };

                    results.success.push(successItem);
                    results.results.push(successItem);
                    results.successCount++;

                } catch (error) {
                    const failedItem = {
                        query,
                        error: error.message,
                        errorCode: error.errorCode || 'SEARCH_FAILED'
                    };

                    results.failed.push(failedItem);
                    results.failedCount++;
                }
            });

            await Promise.all(promises);
        }

        results.took = Date.now() - startTime;

        logBusiness('批量搜索完成', {
            total: results.total,
            success: results.successCount,
            failed: results.failedCount
        });
        logPerformance('批量搜索', results.took, {
            total: results.total,
            success: results.successCount,
            failed: results.failedCount
        });

        return results;

    } catch (error) {
        const duration = Date.now() - startTime;
        logPerformance('批量搜索失败', duration, { count: queries?.length, error: error.message });
    
        if (error instanceof ValidationError) {
            throw error;
        }
    
        logError(error, { context: 'search_batch', queries });
        throw new ServiceUnavailableError(`批量搜索失败: ${error.message}`);
    }
}

/**
 * 执行关键词搜索的具体实现
 * @param {string} keyword - 搜索关键词
 * @param {Array<string>} sources - 音源列表
 * @param {Object} options - 搜索选项
 * @returns {Promise<Object>} 搜索结果
 */
async function performKeywordSearch(keyword, sources, options) {
    // 这里应该实现真正的搜索逻辑
    // 由于UnblockNeteaseMusic主要是解锁功能，搜索功能需要额外实现
    // 可以集成网易云音乐API或其他音乐搜索API
  
    try {
    // 模拟搜索结果（实际项目中需要替换为真实的搜索API调用）
        const mockResults = await mockKeywordSearch(keyword, options);
        return mockResults;
    
    } catch (error) {
        logError(error, { context: 'perform_keyword_search', keyword });
        throw new ServiceUnavailableError('搜索服务暂时不可用');
    }
}

/**
 * 模拟关键词搜索（用于演示，实际项目中需要替换）
 * @param {string} keyword - 搜索关键词
 * @param {Object} options - 搜索选项
 * @returns {Promise<Object>} 模拟搜索结果
 */
async function mockKeywordSearch(keyword, options) {
    const { page, pageSize } = options;
  
    // 模拟一些搜索结果
    const mockSongs = [
        { id: 418602084, title: '稻香', artist: '周杰伦', album: '魔杰座' },
        { id: 186016, title: '青花瓷', artist: '周杰伦', album: '我很忙' },
        { id: 185868, title: '夜曲', artist: '周杰伦', album: '十一月的萧邦' }
    ];

    // 过滤包含关键词的歌曲
    const filteredSongs = mockSongs.filter(song => 
        song.title.includes(keyword) || 
    song.artist.includes(keyword) || 
    song.album.includes(keyword)
    );

    // 分页
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedSongs = filteredSongs.slice(startIndex, endIndex);

    // 格式化结果
    const items = paginatedSongs.map(song => ({
        id: song.id,
        title: song.title,
        artist: song.artist,
        album: song.album,
        duration: 0,
        cover: null,
        source: 'mock'
    }));

    return {
        items,
        total: filteredSongs.length
    };
}

/**
 * 统一搜索入口
 * @param {Object} searchParams - 搜索参数
 * @returns {Promise<Object>} 搜索结果
 */
async function search(searchParams) {
    const { type, query, sources, ...options } = searchParams;

    switch (type) {
    case SEARCH_TYPES.ID:
        return await searchById(query, sources, options);
      
    case SEARCH_TYPES.KEYWORD:
        return await searchByKeyword(query, sources, options);
      
    case SEARCH_TYPES.BATCH:
        return await searchBatch(query, sources, options);
      
    default:
        throw new ValidationError(`不支持的搜索类型: ${type}`);
    }
}

module.exports = {
    search,
    searchById,
    searchByKeyword,
    searchBatch
};
