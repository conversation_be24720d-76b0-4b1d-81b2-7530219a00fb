# 🎵 音乐解锁服务后端系统

基于UnblockNeteaseMusic的音乐解锁服务后端系统，提供完整的RESTful API和前端测试界面。

## 📋 项目概述

本项目是一个完整的音乐解锁服务后端系统，支持：
- 🎵 歌曲信息获取和元数据处理
- 🔍 多种搜索功能（ID搜索、关键词搜索、批量搜索）
- 🔓 音乐解锁服务（单首/批量解锁）
- 🎯 音源管理和状态监控
- 🌐 完整的前端测试界面
- 📊 性能监控和日志记录

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 启动服务
```bash
# 生产环境
npm start

# 开发环境（自动重启）
npm run dev
```

### 访问服务
- **主页面**: http://localhost:50090
- **API基础地址**: http://localhost:50090/api
- **健康检查**: http://localhost:50090/health
- **API文档**: http://localhost:50090/api/docs

## 📚 API接口文档

### 🎵 歌曲信息接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/song/:id` | 获取歌曲信息和解锁链接 |
| GET | `/api/metadata/:id` | 获取歌曲完整元数据 |
| POST | `/api/song/:id/unlock` | 解锁单首歌曲 |
| HEAD | `/api/song/:id` | 检查歌曲可用性 |
| GET | `/api/song/:id/sources` | 获取歌曲可用音源 |
| GET | `/api/song/:id/detail` | 获取歌曲详细信息 |

### 🔍 搜索接口

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/search` | 统一搜索接口 |
| GET | `/api/search/id/:songId` | ID搜索 |
| GET | `/api/search/keyword` | 关键词搜索 |
| POST | `/api/search/batch` | 批量搜索 |
| GET | `/api/search/suggest` | 搜索建议 |
| GET | `/api/search/trending` | 热门搜索 |

### 🔓 解锁服务接口

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/unlock` | 批量解锁歌曲 |
| POST | `/api/unlock/:id` | 单首歌曲解锁 |
| POST | `/api/unlock/quick` | 快速解锁 |
| GET | `/api/unlock/status/:id` | 检查解锁状态 |
| POST | `/api/unlock/status/batch` | 批量检查解锁状态 |

### 🎯 音源管理接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/sources` | 获取音源列表 |
| GET | `/api/sources/:sourceId` | 获取音源详情 |
| POST | `/api/sources/:sourceId/test` | 测试音源可用性 |
| POST | `/api/sources/test/batch` | 批量测试音源 |
| GET | `/api/sources/stats` | 音源统计信息 |
| GET | `/api/sources/config` | 音源配置信息 |

### 🔧 系统接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/health` | 健康检查 |
| GET | `/api` | API信息 |
| GET | `/api/docs` | API文档 |

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
npm run test:all

# 单独运行API测试
npm run test:api

# 运行集成测试
npm run test:integration

# 运行性能测试
npm run test:performance
```

### 前端测试界面
访问 http://localhost:50090 使用可视化测试界面，支持：
- 🎵 歌曲信息测试
- 🔍 搜索功能测试
- 🔓 解锁服务测试
- 🎯 音源管理测试
- 📚 API文档查看

## 📁 项目结构

```
music-unlock-server/
├── src/                    # 源代码目录
│   ├── app.js             # 应用入口文件
│   ├── config/            # 配置文件
│   ├── controllers/       # 控制器层
│   ├── services/          # 服务层
│   ├── middleware/        # 中间件
│   ├── routes/           # 路由定义
│   └── utils/            # 工具函数
├── public/               # 前端静态文件
│   ├── index.html        # 测试页面
│   ├── css/              # 样式文件
│   └── js/               # JavaScript文件
├── tests/                # 测试文件
├── logs/                 # 日志文件
├── project_document/     # 项目文档
└── package.json          # 项目配置
```

## 🔧 配置说明

### 环境变量
创建 `.env` 文件（参考 `.env.example`）：
```env
NODE_ENV=development
PORT=50090
LOG_LEVEL=info
```

### 支持的音源
- QQ音乐 (qq)
- 酷狗音乐 (kugou)
- 酷我音乐 (kuwo)
- 咪咕音乐 (migu)
- JOOX (joox)
- YouTube (youtube)

## 📊 功能特性

### 🎵 核心功能
- ✅ 歌曲信息获取和元数据处理
- ✅ 多种搜索方式支持
- ✅ 单首和批量解锁功能
- ✅ 音源管理和监控
- ✅ 完整的错误处理

### 🛡️ 安全特性
- ✅ 参数验证和清理
- ✅ 请求频率限制
- ✅ CORS跨域支持
- ✅ 安全头部设置
- ✅ 错误信息脱敏

### 📈 性能特性
- ✅ 异步处理和并发控制
- ✅ 响应缓存机制
- ✅ 请求日志记录
- ✅ 性能监控
- ✅ 内存使用优化

### 🎨 用户体验
- ✅ 响应式前端界面
- ✅ 实时状态显示
- ✅ 友好的错误提示
- ✅ 完整的API文档
- ✅ 可视化测试工具

## 🔍 使用示例

### 获取歌曲信息
```bash
curl "http://localhost:50090/api/song/418602084"
```

### 搜索歌曲
```bash
curl -X POST "http://localhost:50090/api/search" \
  -H "Content-Type: application/json" \
  -d '{"type":"keyword","query":"周杰伦","page":1,"pageSize":10}'
```

### 解锁歌曲
```bash
curl -X POST "http://localhost:50090/api/unlock" \
  -H "Content-Type: application/json" \
  -d '{"songIds":[418602084],"minBitrate":320000}'
```

## 📝 开发说明

### 代码规范
- 使用ESLint进行代码检查
- 遵循JavaScript标准规范
- 完整的JSDoc注释
- 统一的错误处理

### 日志系统
- 使用Winston日志框架
- 支持日志轮转和归档
- 分级日志记录
- 请求追踪和性能监控

### 测试覆盖
- 单元测试覆盖核心功能
- 集成测试验证API接口
- 性能测试确保响应速度
- 前端测试界面支持手动测试

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目仅供学习和研究使用，请支持正版音乐。

## 🙏 致谢

- [UnblockNeteaseMusic](https://github.com/UnblockNeteaseMusic/server) - 核心解锁功能
- Express.js - Web框架
- Winston - 日志系统
- Jest - 测试框架

---

**⚠️ 免责声明**: 本项目仅供学习和研究使用，不得用于商业用途。请支持正版音乐，尊重版权。
