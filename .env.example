# 音乐解锁服务配置文件示例
# 复制此文件为 .env 并根据需要修改配置

# 服务器配置
PORT=3000
HOST=localhost
NODE_ENV=development

# 日志配置
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=true

# UnblockNeteaseMusic 配置
# 音源优先级设置 (可选: qq, kugou, kuwo, migu, joox, youtube, ytdlp, bilibili)
MUSIC_SOURCES=qq,kugou,kuwo,migu

# 网易云音乐配置
NETEASE_COOKIE=
ENABLE_FLAC=false
ENABLE_LOCAL_VIP=false
MIN_BR=128000

# QQ音乐配置 (可选)
QQ_COOKIE=

# 咪咕音乐配置 (可选)
MIGU_COOKIE=

# JOOX配置 (可选)
JOOX_COOKIE=

# YouTube配置 (可选)
YOUTUBE_KEY=

# 安全配置
CORS_ORIGIN=*
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 功能开关
ENABLE_SEARCH_ALBUM=true
FOLLOW_SOURCE_ORDER=false
BLOCK_ADS=true
