/**
 * 解锁控制器
 * 处理音乐解锁相关的API请求
 */

const { unlockSong, unlockSongsBatch } = require('../services/unlockService');
const { isValidSongId } = require('../services/musicService');
const { success, badRequest, notFound } = require('../utils/response');
const { asyncHandler } = require('../middleware/errorHandler');
const { logBusiness } = require('../middleware/logger');
const { API_LIMITS } = require('../utils/constants');

/**
 * 批量解锁歌曲
 * POST /api/unlock
 */
const unlockSongs = asyncHandler(async (req, res) => {
    const { songIds, sources, minBitrate = 128000, detailed = false } = req.body;

    logBusiness('批量解锁请求', { count: songIds?.length, sources, minBitrate, detailed });

    // 验证歌曲ID列表
    if (!Array.isArray(songIds) || songIds.length === 0) {
        return badRequest(res, '歌曲ID列表不能为空');
    }

    if (songIds.length > API_LIMITS.MAX_BATCH_SIZE) {
        return badRequest(res, `批量解锁数量超限，最大: ${API_LIMITS.MAX_BATCH_SIZE}`);
    }

    // 验证每个歌曲ID
    const invalidIds = songIds.filter(id => !isValidSongId(id));
    if (invalidIds.length > 0) {
        return badRequest(res, `无效的歌曲ID: ${invalidIds.join(', ')}`);
    }

    // 检查重复ID
    const uniqueIds = [...new Set(songIds.map(id => parseInt(id)))];
    if (uniqueIds.length !== songIds.length) {
        return badRequest(res, '歌曲ID列表包含重复项');
    }

    // 验证音质要求
    const validBitrates = [128000, 192000, 320000, 999000];
    if (!validBitrates.includes(minBitrate)) {
        return badRequest(res, `无效的音质要求，支持的音质: ${validBitrates.join(', ')}`);
    }

    const result = await unlockSongsBatch(uniqueIds, sources, {
        minBitrate,
        detailed
    });

    // 添加统计信息
    const response = {
        ...result,
        statistics: {
            total: result.total,
            successCount: result.successCount,
            failedCount: result.failedCount,
            successRate: ((result.successCount / result.total) * 100).toFixed(2) + '%'
        }
    };

    success(res, response, `批量解锁完成，成功: ${result.successCount}/${result.total}`);
});

/**
 * 单首歌曲解锁
 * POST /api/unlock/:id
 */
const unlockSingleSong = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { sources, minBitrate = 128000 } = req.body;

    logBusiness('单首解锁请求', { songId: id, sources, minBitrate });

    // 验证歌曲ID
    if (!isValidSongId(id)) {
        return badRequest(res, '无效的歌曲ID格式');
    }

    // 验证音质要求
    const validBitrates = [128000, 192000, 320000, 999000];
    if (!validBitrates.includes(minBitrate)) {
        return badRequest(res, `无效的音质要求，支持的音质: ${validBitrates.join(', ')}`);
    }

    try {
        const result = await unlockSong(id, sources);

        // 检查音质是否满足要求
        if (result.bitrate < minBitrate) {
            return badRequest(res, `音质不满足要求，当前: ${result.bitrate}kbps, 要求: ${minBitrate}kbps`);
        }

        success(res, result, '歌曲解锁成功');

    } catch (error) {
        if (error.statusCode === 404) {
            return notFound(res, `歌曲 ${id} 无法解锁`);
        }
    
        throw error;
    }
});

/**
 * 快速解锁（仅返回播放URL）
 * POST /api/unlock/quick
 */
const quickUnlock = asyncHandler(async (req, res) => {
    const { songIds, sources } = req.body;

    logBusiness('快速解锁请求', { count: songIds?.length, sources });

    // 验证歌曲ID列表
    if (!Array.isArray(songIds) || songIds.length === 0) {
        return badRequest(res, '歌曲ID列表不能为空');
    }

    if (songIds.length > API_LIMITS.MAX_BATCH_SIZE) {
        return badRequest(res, `批量解锁数量超限，最大: ${API_LIMITS.MAX_BATCH_SIZE}`);
    }

    // 验证每个歌曲ID
    const invalidIds = songIds.filter(id => !isValidSongId(id));
    if (invalidIds.length > 0) {
        return badRequest(res, `无效的歌曲ID: ${invalidIds.join(', ')}`);
    }

    const result = await unlockSongsBatch(songIds, sources, {
        minBitrate: 128000,
        detailed: false
    });

    // 只返回成功的结果，简化格式
    const quickResults = result.success.map(item => ({
        songId: item.songId,
        url: item.url,
        source: item.source
    }));

    const response = {
        results: quickResults,
        total: result.total,
        successCount: result.successCount,
        failedIds: result.failed.map(item => item.songId)
    };

    success(res, response, `快速解锁完成，成功: ${result.successCount}/${result.total}`);
});

/**
 * 检查解锁状态
 * GET /api/unlock/status/:id
 */
const checkUnlockStatus = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { sources } = req.query;

    logBusiness('检查解锁状态', { songId: id, sources });

    // 验证歌曲ID
    if (!isValidSongId(id)) {
        return badRequest(res, '无效的歌曲ID格式');
    }

    try {
        const sourcesArray = sources ? sources.split(',').map(s => s.trim()) : null;
    
        // 尝试解锁来检查状态
        const result = await unlockSong(id, sourcesArray);

        const status = {
            songId: parseInt(id),
            available: true,
            source: result.source,
            sourceName: result.sourceName,
            bitrate: result.bitrate,
            quality: getQualityDescription(result.bitrate),
            checkedAt: new Date().toISOString()
        };

        success(res, status, '解锁状态检查完成');

    } catch (error) {
        if (error.statusCode === 404) {
            const status = {
                songId: parseInt(id),
                available: false,
                reason: '无可用音源',
                checkedAt: new Date().toISOString()
            };
      
            success(res, status, '歌曲暂时无法解锁');
        } else {
            throw error;
        }
    }
});

/**
 * 批量检查解锁状态
 * POST /api/unlock/status/batch
 */
const checkBatchUnlockStatus = asyncHandler(async (req, res) => {
    const { songIds, sources } = req.body;

    logBusiness('批量检查解锁状态', { count: songIds?.length, sources });

    // 验证歌曲ID列表
    if (!Array.isArray(songIds) || songIds.length === 0) {
        return badRequest(res, '歌曲ID列表不能为空');
    }

    if (songIds.length > API_LIMITS.MAX_BATCH_SIZE) {
        return badRequest(res, `批量检查数量超限，最大: ${API_LIMITS.MAX_BATCH_SIZE}`);
    }

    // 验证每个歌曲ID
    const invalidIds = songIds.filter(id => !isValidSongId(id));
    if (invalidIds.length > 0) {
        return badRequest(res, `无效的歌曲ID: ${invalidIds.join(', ')}`);
    }

    const results = [];
    const concurrency = 3; // 限制并发数

    // 分批处理
    for (let i = 0; i < songIds.length; i += concurrency) {
        const batch = songIds.slice(i, i + concurrency);

        const batchPromises = batch.map(async (songId) => {
            try {
                const result = await unlockSong(songId, sources);
                return {
                    songId: parseInt(songId),
                    available: true,
                    source: result.source,
                    sourceName: result.sourceName,
                    bitrate: result.bitrate,
                    quality: getQualityDescription(result.bitrate)
                };
            } catch (error) {
                return {
                    songId: parseInt(songId),
                    available: false,
                    reason: error.message
                };
            }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
    }

    const summary = {
        total: results.length,
        available: results.filter(r => r.available).length,
        unavailable: results.filter(r => !r.available).length,
        checkedAt: new Date().toISOString()
    };

    const response = {
        results,
        summary
    };

    success(res, response, `批量状态检查完成，可用: ${summary.available}/${summary.total}`);
});

/**
 * 根据比特率获取音质描述
 * @param {number} bitrate - 比特率
 * @returns {string} 音质描述
 */
function getQualityDescription(bitrate) {
    if (bitrate >= 999000) return '无损';
    if (bitrate >= 320000) return '极高';
    if (bitrate >= 192000) return '较高';
    if (bitrate >= 128000) return '标准';
    return '低质量';
}

module.exports = {
    unlockSongs,
    unlockSingleSong,
    quickUnlock,
    checkUnlockStatus,
    checkBatchUnlockStatus
};
