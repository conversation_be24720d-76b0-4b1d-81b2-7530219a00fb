/**
 * 搜索相关路由
 */

const express = require('express');
const router = express.Router();

// 导入控制器
const {
    searchSongs,
    searchBySongId,
    searchByKeywords,
    searchMultiple,
    getSearchSuggestions,
    getTrendingSearches
} = require('../controllers/searchController');

// 导入验证中间件
const { validators } = require('../middleware/validator');

// 统一搜索接口
router.post('/', validators.validateSearchSongs, searchSongs);

// ID搜索
router.get('/id/:songId', searchBySongId);

// 关键词搜索
router.get('/keyword', searchByKeywords);

// 批量搜索
router.post('/batch', searchMultiple);

// 搜索建议
router.get('/suggest', getSearchSuggestions);

// 热门搜索
router.get('/trending', getTrendingSearches);

module.exports = router;
