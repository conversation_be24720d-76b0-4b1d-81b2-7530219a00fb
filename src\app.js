/**
 * 音乐解锁服务主应用入口
 * 基于Express.js和UnblockNeteaseMusic的音乐解锁服务后端
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');

// 导入配置和工具
const config = require('./config/config');
const { logger, requestLogger } = require('./middleware/logger');
const { errorHandler, notFoundHandler, setupProcessErrorHandlers } = require('./middleware/errorHandler');
const { success } = require('./utils/response');

// 设置进程级错误处理
setupProcessErrorHandlers();

// 创建Express应用
const app = express();

// 信任代理（用于获取真实IP）
app.set('trust proxy', 1);

// 安全中间件
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ['\'self\''],
            styleSrc: ['\'self\'', '\'unsafe-inline\''],
            scriptSrc: ['\'self\'', '\'unsafe-inline\''],
            imgSrc: ['\'self\'', 'data:', 'https:'],
        },
    },
}));

// CORS配置
app.use(cors({
    origin: config.security.corsOrigin,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// 请求体解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use(express.static(path.join(__dirname, '../public')));

// 请求日志中间件
app.use(requestLogger);

// API限流中间件
const limiter = rateLimit({
    windowMs: config.security.rateLimitWindowMs,
    max: config.security.rateLimitMaxRequests,
    message: {
        code: 429,
        message: '请求过于频繁，请稍后再试',
        timestamp: new Date().toISOString()
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use('/api', limiter);

// 健康检查接口
app.get('/health', (req, res) => {
    success(res, {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: require('../package.json').version,
        environment: config.server.env,
        uptime: process.uptime()
    }, '服务运行正常');
});

// 根路径重定向到测试页面
app.get('/', (req, res) => {
    res.redirect('/index.html');
});

// API路由
app.use('/api', require('./routes'));

// 404错误处理
app.use(notFoundHandler);

// 全局错误处理中间件
app.use(errorHandler);

// 启动服务器
const server = app.listen(config.server.port, config.server.host, () => {
    logger.info('🎵 音乐解锁服务启动成功', {
        host: config.server.host,
        port: config.server.port,
        environment: config.server.env,
        pid: process.pid
    });
  
    logger.info(`📱 测试页面地址: http://${config.server.host}:${config.server.port}`);
    logger.info(`🔍 健康检查地址: http://${config.server.host}:${config.server.port}/health`);
    logger.info(`🎯 API基础地址: http://${config.server.host}:${config.server.port}/api`);
});

// 优雅关闭处理
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

function gracefulShutdown(signal) {
    logger.info(`收到${signal}信号，开始优雅关闭服务器...`);
  
    server.close((err) => {
        if (err) {
            logger.error('服务器关闭时发生错误:', err);
            process.exit(1);
        }
    
        logger.info('服务器已优雅关闭');
        process.exit(0);
    });
  
    // 强制关闭超时
    setTimeout(() => {
        logger.error('强制关闭服务器');
        process.exit(1);
    }, 10000);
}

module.exports = app;
