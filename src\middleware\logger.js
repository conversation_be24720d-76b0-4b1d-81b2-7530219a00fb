/**
 * 日志中间件模块
 * 基于Winston实现的日志记录系统，支持文件和控制台输出
 */

const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const config = require('../config/config');

// 自定义日志格式
const logFormat = winston.format.combine(
    winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ level, message, timestamp, stack, ...meta }) => {
        let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
        // 如果有额外的元数据，添加到日志中
        if (Object.keys(meta).length > 0) {
            log += ` | ${JSON.stringify(meta)}`;
        }
    
        // 如果有错误堆栈，添加到日志中
        if (stack) {
            log += `\n${stack}`;
        }
    
        return log;
    })
);

// 创建传输器数组
const transports = [];

// 控制台输出配置
if (config.logging.consoleEnabled) {
    transports.push(
        new winston.transports.Console({
            format: winston.format.combine(
                winston.format.colorize(),
                logFormat
            )
        })
    );
}

// 文件输出配置
if (config.logging.fileEnabled) {
    // 错误日志文件
    transports.push(
        new DailyRotateFile({
            filename: path.join('logs', 'error-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            level: 'error',
            maxFiles: config.logging.maxFiles,
            maxSize: config.logging.maxSize,
            format: logFormat
        })
    );

    // 组合日志文件
    transports.push(
        new DailyRotateFile({
            filename: path.join('logs', 'combined-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            maxFiles: config.logging.maxFiles,
            maxSize: config.logging.maxSize,
            format: logFormat
        })
    );

    // 访问日志文件
    transports.push(
        new DailyRotateFile({
            filename: path.join('logs', 'access-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            level: 'info',
            maxFiles: config.logging.maxFiles,
            maxSize: config.logging.maxSize,
            format: logFormat
        })
    );
}

// 创建logger实例
const logger = winston.createLogger({
    level: config.logging.level,
    format: logFormat,
    transports,
    // 处理未捕获的异常
    exceptionHandlers: config.logging.fileEnabled ? [
        new DailyRotateFile({
            filename: path.join('logs', 'exceptions-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            maxFiles: config.logging.maxFiles,
            maxSize: config.logging.maxSize
        })
    ] : [],
    // 处理未处理的Promise拒绝
    rejectionHandlers: config.logging.fileEnabled ? [
        new DailyRotateFile({
            filename: path.join('logs', 'rejections-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            maxFiles: config.logging.maxFiles,
            maxSize: config.logging.maxSize
        })
    ] : []
});

/**
 * Express请求日志中间件
 * 记录每个HTTP请求的详细信息
 */
function requestLogger(req, res, next) {
    const startTime = Date.now();
  
    // 记录请求开始
    logger.info('请求开始', {
        method: req.method,
        url: req.url,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        requestId: req.id || generateRequestId()
    });

    // 监听响应结束事件
    res.on('finish', () => {
        const duration = Date.now() - startTime;
        const logLevel = res.statusCode >= 400 ? 'warn' : 'info';
    
        logger[logLevel]('请求完成', {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            duration: `${duration}ms`,
            ip: req.ip || req.connection.remoteAddress,
            requestId: req.id || 'unknown'
        });
    });

    next();
}

/**
 * 生成请求ID
 * @returns {string} 唯一的请求ID
 */
function generateRequestId() {
    return Math.random().toString(36).substr(2, 9);
}

/**
 * 错误日志记录函数
 * @param {Error} error - 错误对象
 * @param {Object} context - 上下文信息
 */
function logError(error, context = {}) {
    logger.error('应用错误', {
        message: error.message,
        stack: error.stack,
        ...context
    });
}

/**
 * 性能日志记录函数
 * @param {string} operation - 操作名称
 * @param {number} duration - 执行时间(毫秒)
 * @param {Object} metadata - 元数据
 */
function logPerformance(operation, duration, metadata = {}) {
    logger.info('性能监控', {
        operation,
        duration: `${duration}ms`,
        ...metadata
    });
}

/**
 * 业务日志记录函数
 * @param {string} action - 业务动作
 * @param {Object} data - 业务数据
 */
function logBusiness(action, data = {}) {
    logger.info('业务操作', {
        action,
        ...data
    });
}

module.exports = {
    logger,
    requestLogger,
    logError,
    logPerformance,
    logBusiness
};
