/**
 * 音乐解锁服务集成测试脚本 - 修复版
 * 测试完整的业务流程和系统集成
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// 配置axios默认设置
axios.defaults.timeout = 15000;
axios.defaults.headers.common['User-Agent'] = 'Integration-Test/1.0';

// 测试配置
const BASE_URL = 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api`;

// 测试数据
const TEST_DATA = {
    songs: [
        { id: '418602084', name: '稻香', artist: '周杰伦' }
    ],
    searchKeywords: ['周杰伦', '稻香']
};

/**
 * 集成测试管理器
 */
class IntegrationTestManager {
    constructor() {
        this.testResults = [];
        this.errors = [];
    }

    /**
     * 记录测试结果
     */
    recordTest(testName, success, details = {}) {
        const result = {
            testName,
            success,
            timestamp: new Date().toISOString(),
            ...details
        };
        
        this.testResults.push(result);
        
        if (success) {
            console.log(`✅ ${testName}`);
        } else {
            console.log(`❌ ${testName}: ${details.error || '测试失败'}`);
            this.errors.push(result);
        }
        
        return result;
    }

    /**
     * HTTP请求封装
     */
    async request(method, url, data = null) {
        try {
            const config = {
                method,
                url,
                timeout: 15000,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Integration-Test/1.0'
                },
                ...(data && { data })
            };

            const response = await axios(config);
            return {
                success: true,
                status: response.status,
                data: response.data
            };
        } catch (error) {
            console.log(`请求失败: ${method} ${url} - ${error.message}`);
            return {
                success: false,
                status: error.response?.status || 0,
                error: error.message,
                data: error.response?.data
            };
        }
    }

    /**
     * 测试1: 系统健康检查
     */
    async testSystemHealth() {
        console.log('\n🏥 测试系统健康状态...');
        
        const result = await this.request('GET', `${BASE_URL}/health`);
        
        if (result.success && result.data.code === 200 && result.data.data.status === 'healthy') {
            return this.recordTest('系统健康检查', true, { 
                status: result.data.data.status 
            });
        } else {
            return this.recordTest('系统健康检查', false, { 
                error: result.error || '健康检查失败' 
            });
        }
    }

    /**
     * 测试2: API基础功能
     */
    async testApiBasics() {
        console.log('\n📋 测试API基础功能...');
        
        // 测试API信息
        const apiInfoResult = await this.request('GET', API_BASE);
        const apiInfoSuccess = apiInfoResult.success && 
                              apiInfoResult.data.name === '音乐解锁服务API';
        
        this.recordTest('API信息获取', apiInfoSuccess, {
            version: apiInfoResult.data?.version
        });

        // 测试API文档
        const docsResult = await this.request('GET', `${API_BASE}/docs`);
        const docsSuccess = docsResult.success && 
                           docsResult.data.title === '音乐解锁服务API文档';
        
        this.recordTest('API文档获取', docsSuccess, {
            endpointCount: docsResult.data?.endpoints?.length
        });

        return apiInfoSuccess && docsSuccess;
    }

    /**
     * 测试3: 歌曲信息功能
     */
    async testSongInfo() {
        console.log('\n🎵 测试歌曲信息功能...');
        
        const testSong = TEST_DATA.songs[0];
        let allSuccess = true;

        // 1. 获取歌曲基本信息
        const songInfoResult = await this.request('GET', `${API_BASE}/song/${testSong.id}`);
        const songInfoSuccess = songInfoResult.success && 
                               songInfoResult.data.code === 200;
        
        this.recordTest(`获取歌曲信息 - ${testSong.name}`, songInfoSuccess);
        allSuccess = allSuccess && songInfoSuccess;

        // 2. 获取歌曲元数据
        const metadataResult = await this.request('GET', `${API_BASE}/metadata/${testSong.id}`);
        const metadataSuccess = metadataResult.success && 
                               metadataResult.data.code === 200;
        
        this.recordTest(`获取歌曲元数据 - ${testSong.name}`, metadataSuccess);
        allSuccess = allSuccess && metadataSuccess;

        // 3. 检查歌曲可用性
        const availabilityResult = await this.request('HEAD', `${API_BASE}/song/${testSong.id}`);
        const availabilitySuccess = availabilityResult.success;
        
        this.recordTest(`检查歌曲可用性 - ${testSong.name}`, availabilitySuccess);
        allSuccess = allSuccess && availabilitySuccess;

        return allSuccess;
    }

    /**
     * 测试4: 搜索功能
     */
    async testSearchFunctions() {
        console.log('\n🔍 测试搜索功能...');
        
        let allSuccess = true;

        // 1. 关键词搜索
        const keywordSearchResult = await this.request('POST', `${API_BASE}/search`, {
            type: 'keyword',
            query: TEST_DATA.searchKeywords[0],
            page: 1,
            pageSize: 10
        });
        const keywordSearchSuccess = keywordSearchResult.success && 
                                    keywordSearchResult.data.code === 200;
        
        this.recordTest(`关键词搜索 - ${TEST_DATA.searchKeywords[0]}`, keywordSearchSuccess);
        allSuccess = allSuccess && keywordSearchSuccess;

        // 2. ID搜索
        const idSearchResult = await this.request('GET', `${API_BASE}/search/id/${TEST_DATA.songs[0].id}`);
        const idSearchSuccess = idSearchResult.success && 
                               idSearchResult.data.code === 200;
        
        this.recordTest(`ID搜索 - ${TEST_DATA.songs[0].id}`, idSearchSuccess);
        allSuccess = allSuccess && idSearchSuccess;

        return allSuccess;
    }

    /**
     * 测试5: 解锁功能
     */
    async testUnlockFunctions() {
        console.log('\n🔓 测试解锁功能...');
        
        let allSuccess = true;

        // 1. 批量解锁
        const batchUnlockResult = await this.request('POST', `${API_BASE}/unlock`, {
            songIds: [parseInt(TEST_DATA.songs[0].id)],
            minBitrate: 128000
        });
        const batchUnlockSuccess = batchUnlockResult.success && 
                                  batchUnlockResult.data.code === 200;
        
        this.recordTest('批量解锁', batchUnlockSuccess);
        allSuccess = allSuccess && batchUnlockSuccess;

        return allSuccess;
    }

    /**
     * 测试6: 音源管理
     */
    async testSourceManagement() {
        console.log('\n🎯 测试音源管理...');
        
        let allSuccess = true;

        // 1. 获取音源列表
        const sourcesResult = await this.request('GET', `${API_BASE}/sources`);
        const sourcesSuccess = sourcesResult.success && 
                              sourcesResult.data.code === 200;
        
        this.recordTest('获取音源列表', sourcesSuccess);
        allSuccess = allSuccess && sourcesSuccess;

        return allSuccess;
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🎵 音乐解锁服务集成测试开始');
        console.log('============================================================');

        const startTime = performance.now();

        // 执行所有测试
        await this.testSystemHealth();
        await this.testApiBasics();
        await this.testSongInfo();
        await this.testSearchFunctions();
        await this.testUnlockFunctions();
        await this.testSourceManagement();

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        // 生成测试报告
        this.generateReport(duration);
    }

    /**
     * 生成测试报告
     */
    generateReport(duration) {
        console.log('\n📊 集成测试报告');
        console.log('============================================================');
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.success).length;
        const failedTests = this.errors.length;
        const successRate = ((passedTests / totalTests) * 100).toFixed(2);

        console.log(`总测试数: ${totalTests}`);
        console.log(`通过数: ${passedTests}`);
        console.log(`失败数: ${failedTests}`);
        console.log(`成功率: ${successRate}%`);
        console.log(`总耗时: ${duration}ms`);

        if (failedTests === 0) {
            console.log('\n✅ 集成测试全部通过！');
        } else {
            console.log('\n❌ 集成测试未通过，需要修复问题。');
        }

        console.log('\n🎉 集成测试完成！');
    }
}

// 运行测试
async function main() {
    const testManager = new IntegrationTestManager();
    await testManager.runAllTests();
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    main().catch(console.error);
}

module.exports = IntegrationTestManager;
