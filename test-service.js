#!/usr/bin/env node

/**
 * 音乐解锁服务测试脚本
 * 快速验证服务是否正常运行
 */

const http = require('http');

const BASE_URL = 'http://localhost:50090';

/**
 * 发送HTTP请求
 */
function makeRequest(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(path, BASE_URL);
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Music-Unlock-Test/1.0'
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(body);
                    resolve({
                        status: res.statusCode,
                        data: jsonData,
                        success: res.statusCode >= 200 && res.statusCode < 300
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        data: body,
                        success: res.statusCode >= 200 && res.statusCode < 300
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

/**
 * 测试函数
 */
async function runTest(name, testFn) {
    try {
        await testFn();
        console.log(`✅ ${name}`);
        return true;
    } catch (error) {
        console.log(`❌ ${name}: ${error.message}`);
        return false;
    }
}

/**
 * 主测试函数
 */
async function main() {
    console.log('🎵 音乐解锁服务测试开始\n');
    
    let passedTests = 0;
    let totalTests = 0;

    // 测试1: 健康检查
    totalTests++;
    if (await runTest('健康检查', async () => {
        const response = await makeRequest('/health');
        if (!response.success || response.data.code !== 200) {
            throw new Error(`状态码: ${response.status}`);
        }
    })) {
        passedTests++;
    }

    // 测试2: API信息
    totalTests++;
    if (await runTest('API信息', async () => {
        const response = await makeRequest('/api');
        if (!response.success || !response.data.name) {
            throw new Error(`状态码: ${response.status}`);
        }
    })) {
        passedTests++;
    }

    // 测试3: API文档
    totalTests++;
    if (await runTest('API文档', async () => {
        const response = await makeRequest('/api/docs');
        if (!response.success || !response.data.title) {
            throw new Error(`状态码: ${response.status}`);
        }
    })) {
        passedTests++;
    }

    // 测试4: 歌曲信息
    totalTests++;
    if (await runTest('歌曲信息', async () => {
        const response = await makeRequest('/api/song/418602084');
        if (!response.success || response.data.code !== 200) {
            throw new Error(`状态码: ${response.status}`);
        }
    })) {
        passedTests++;
    }

    // 测试5: 歌曲元数据
    totalTests++;
    if (await runTest('歌曲元数据', async () => {
        const response = await makeRequest('/api/metadata/418602084');
        if (!response.success || response.data.code !== 200) {
            throw new Error(`状态码: ${response.status}`);
        }
    })) {
        passedTests++;
    }

    // 测试6: 音源列表
    totalTests++;
    if (await runTest('音源列表', async () => {
        const response = await makeRequest('/api/sources');
        if (!response.success || response.data.code !== 200) {
            throw new Error(`状态码: ${response.status}`);
        }
    })) {
        passedTests++;
    }

    // 测试7: 前端页面
    totalTests++;
    if (await runTest('前端页面', async () => {
        const response = await makeRequest('/');
        if (!response.success) {
            throw new Error(`状态码: ${response.status}`);
        }
    })) {
        passedTests++;
    }

    // 测试8: 错误处理
    totalTests++;
    if (await runTest('错误处理', async () => {
        const response = await makeRequest('/api/song/invalid');
        if (response.status !== 400) {
            throw new Error(`期望400，实际: ${response.status}`);
        }
    })) {
        passedTests++;
    }

    // 输出测试结果
    console.log('\n📊 测试结果:');
    console.log('='.repeat(40));
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${totalTests - passedTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(2)}%`);
    console.log('='.repeat(40));

    if (passedTests === totalTests) {
        console.log('\n🎉 所有测试通过！服务运行正常。');
        console.log('\n📋 可用地址:');
        console.log(`   - 主页面: ${BASE_URL}`);
        console.log(`   - API文档: ${BASE_URL}/api/docs`);
        console.log(`   - 健康检查: ${BASE_URL}/health`);
        return true;
    } else {
        console.log('\n⚠️ 部分测试失败，请检查服务状态。');
        return false;
    }
}

// 运行测试
if (require.main === module) {
    main()
        .then((success) => {
            process.exit(success ? 0 : 1);
        })
        .catch((error) => {
            console.error('\n💥 测试过程中发生错误:', error.message);
            process.exit(1);
        });
}

module.exports = main;
