/**
 * 音乐解锁服务API测试脚本
 * 使用Jest进行API接口测试
 */

const request = require('supertest');
const app = require('../src/app');

// 测试配置
const TEST_SONG_ID = '418602084'; // 周杰伦-稻香
const TEST_SEARCH_KEYWORD = '周杰伦';
const API_BASE = '/api';

describe('音乐解锁服务API测试', () => {
    
    // 健康检查测试
    describe('健康检查', () => {
        test('GET /health - 应该返回服务健康状态', async () => {
            const response = await request(app)
                .get('/health')
                .expect(200);

            expect(response.body.code).toBe(200);
            expect(response.body.data.status).toBe('healthy');
        });
    });

    // API信息测试
    describe('API信息', () => {
        test('GET /api - 应该返回API基本信息', async () => {
            const response = await request(app)
                .get(API_BASE)
                .expect(200);

            expect(response.body.name).toBe('音乐解锁服务API');
            expect(response.body.version).toBeDefined();
        });

        test('GET /api/docs - 应该返回API文档', async () => {
            const response = await request(app)
                .get(`${API_BASE}/docs`)
                .expect(200);

            expect(response.body.title).toBe('音乐解锁服务API文档');
            expect(response.body.endpoints).toBeDefined();
        });
    });

    // 歌曲信息API测试
    describe('歌曲信息API', () => {
        test('GET /api/song/:id - 应该返回歌曲信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/song/${TEST_SONG_ID}`)
                .expect(200);

            expect(response.body.code).toBe(200);
            expect(response.body.data.id).toBe(parseInt(TEST_SONG_ID));
        });

        test('GET /api/metadata/:id - 应该返回歌曲元数据', async () => {
            const response = await request(app)
                .get(`${API_BASE}/metadata/${TEST_SONG_ID}`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.songId).toBe(parseInt(TEST_SONG_ID));
        });

        test('HEAD /api/song/:id - 应该检查歌曲可用性', async () => {
            await request(app)
                .head(`${API_BASE}/song/${TEST_SONG_ID}`)
                .expect(200);
        });

        test('GET /api/song/:id/sources - 应该返回歌曲音源列表', async () => {
            const response = await request(app)
                .get(`${API_BASE}/song/${TEST_SONG_ID}/sources`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.sources)).toBe(true);
        });

        test('GET /api/song/:id/detail - 应该返回歌曲详细信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/song/${TEST_SONG_ID}/detail`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.songId).toBe(parseInt(TEST_SONG_ID));
        });
    });

    // 搜索API测试
    describe('搜索API', () => {
        test('POST /api/search - 关键词搜索', async () => {
            const response = await request(app)
                .post(`${API_BASE}/search`)
                .send({
                    type: 'keyword',
                    query: TEST_SEARCH_KEYWORD,
                    page: 1,
                    pageSize: 10
                })
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.songs)).toBe(true);
        });

        test('GET /api/search/id/:songId - ID搜索', async () => {
            const response = await request(app)
                .get(`${API_BASE}/search/id/${TEST_SONG_ID}`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.songId).toBe(parseInt(TEST_SONG_ID));
        });

        test('GET /api/search/keyword - 关键词搜索', async () => {
            const response = await request(app)
                .get(`${API_BASE}/search/keyword`)
                .query({
                    q: TEST_SEARCH_KEYWORD,
                    page: 1,
                    pageSize: 10
                })
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.songs)).toBe(true);
        });

        test('POST /api/search/batch - 批量搜索', async () => {
            const response = await request(app)
                .post(`${API_BASE}/search/batch`)
                .send({
                    queries: [TEST_SONG_ID, '186016']
                })
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.results)).toBe(true);
        });

        test('GET /api/search/suggest - 搜索建议', async () => {
            const response = await request(app)
                .get(`${API_BASE}/search/suggest`)
                .query({ q: '周杰' })
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.suggestions)).toBe(true);
        });

        test('GET /api/search/trending - 热门搜索', async () => {
            const response = await request(app)
                .get(`${API_BASE}/search/trending`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.trending)).toBe(true);
        });
    });

    // 解锁API测试
    describe('解锁API', () => {
        test('POST /api/unlock - 批量解锁', async () => {
            const response = await request(app)
                .post(`${API_BASE}/unlock`)
                .send({
                    songIds: [parseInt(TEST_SONG_ID)],
                    minBitrate: 128000
                })
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.results)).toBe(true);
        });

        test('POST /api/unlock/:id - 单首解锁', async () => {
            const response = await request(app)
                .post(`${API_BASE}/unlock/${TEST_SONG_ID}`)
                .send({
                    minBitrate: 128000
                })
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.songId).toBe(parseInt(TEST_SONG_ID));
        });

        test('POST /api/unlock/quick - 快速解锁', async () => {
            const response = await request(app)
                .post(`${API_BASE}/unlock/quick`)
                .send({
                    songIds: [parseInt(TEST_SONG_ID)]
                })
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.results)).toBe(true);
        });

        test('GET /api/unlock/status/:id - 检查解锁状态', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock/status/${TEST_SONG_ID}`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.songId).toBe(parseInt(TEST_SONG_ID));
        });

        test('POST /api/unlock/status/batch - 批量检查解锁状态', async () => {
            const response = await request(app)
                .post(`${API_BASE}/unlock/status/batch`)
                .send({
                    songIds: [parseInt(TEST_SONG_ID)]
                })
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.results)).toBe(true);
        });
    });

    // 音源管理API测试
    describe('音源管理API', () => {
        test('GET /api/sources - 获取音源列表', async () => {
            const response = await request(app)
                .get(`${API_BASE}/sources`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.sources)).toBe(true);
        });

        test('GET /api/sources/:sourceId - 获取音源详情', async () => {
            const response = await request(app)
                .get(`${API_BASE}/sources/qq`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.sourceId).toBe('qq');
        });

        test('POST /api/sources/:sourceId/test - 测试音源', async () => {
            const response = await request(app)
                .post(`${API_BASE}/sources/qq/test`)
                .send({})
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.sourceId).toBe('qq');
        });

        test('POST /api/sources/test/batch - 批量测试音源', async () => {
            const response = await request(app)
                .post(`${API_BASE}/sources/test/batch`)
                .send({
                    sourceIds: ['qq', 'kugou']
                })
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.results)).toBe(true);
        });

        test('GET /api/sources/stats - 获取音源统计', async () => {
            const response = await request(app)
                .get(`${API_BASE}/sources/stats`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.totalSources).toBeDefined();
        });

        test('GET /api/sources/config - 获取音源配置', async () => {
            const response = await request(app)
                .get(`${API_BASE}/sources/config`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.sources).toBeDefined();
        });
    });

    // 错误处理测试
    describe('错误处理', () => {
        test('GET /api/song/invalid - 无效歌曲ID应返回400', async () => {
            const response = await request(app)
                .get(`${API_BASE}/song/invalid`)
                .expect(400);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('VALIDATION_ERROR');
        });

        test('GET /api/nonexistent - 不存在的路径应返回404', async () => {
            const response = await request(app)
                .get(`${API_BASE}/nonexistent`)
                .expect(404);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('NOT_FOUND');
        });

        test('POST /api/search - 缺少必需参数应返回400', async () => {
            const response = await request(app)
                .post(`${API_BASE}/search`)
                .send({})
                .expect(400);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('VALIDATION_ERROR');
        });
    });
});

// 性能测试
describe('性能测试', () => {
    test('API响应时间应在合理范围内', async () => {
        const startTime = Date.now();
        
        await request(app)
            .get(`${API_BASE}/song/${TEST_SONG_ID}`)
            .expect(200);
        
        const responseTime = Date.now() - startTime;
        expect(responseTime).toBeLessThan(5000); // 5秒内响应
    });

    test('并发请求处理', async () => {
        const promises = Array(10).fill().map(() => 
            request(app)
                .get(`${API_BASE}/song/${TEST_SONG_ID}`)
                .expect(200)
        );
        
        const results = await Promise.all(promises);
        expect(results.length).toBe(10);
        results.forEach(result => {
            expect(result.body.success).toBe(true);
        });
    });
});
