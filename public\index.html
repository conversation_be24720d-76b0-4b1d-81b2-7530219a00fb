<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐解锁服务 - API测试页面</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎵</text></svg>">
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <header class="header">
            <h1>🎵 音乐解锁服务</h1>
            <p class="subtitle">基于UnblockNeteaseMusic的音乐解锁服务后端API测试页面</p>
            <div class="status-bar">
                <span id="service-status" class="status-indicator">检查中...</span>
                <span id="api-version" class="version-info"></span>
            </div>
        </header>

        <!-- 功能导航 -->
        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="song">歌曲信息</button>
            <button class="tab-btn" data-tab="search">搜索功能</button>
            <button class="tab-btn" data-tab="unlock">解锁服务</button>
            <button class="tab-btn" data-tab="sources">音源管理</button>
            <button class="tab-btn" data-tab="docs">API文档</button>
        </nav>

        <!-- 歌曲信息测试 -->
        <div id="song-tab" class="tab-content active">
            <div class="section">
                <h2>🎵 歌曲信息获取</h2>
                <div class="form-group">
                    <label for="song-id">歌曲ID:</label>
                    <input type="text" id="song-id" placeholder="例如: 418602084 (周杰伦-稻香)" value="418602084">
                    <small>网易云音乐歌曲ID，可从歌曲链接中获取</small>
                </div>
                <div class="form-group">
                    <label for="song-sources">指定音源 (可选):</label>
                    <input type="text" id="song-sources" placeholder="例如: qq,kugou,kuwo">
                    <small>多个音源用逗号分隔，留空使用默认音源</small>
                </div>
                <div class="button-group">
                    <button onclick="getSongInfo()" class="btn btn-primary">获取歌曲信息</button>
                    <button onclick="getSongMetadata()" class="btn btn-secondary">仅获取元数据</button>
                    <button onclick="checkSongAvailability()" class="btn btn-info">检查可用性</button>
                </div>
                <div id="song-result" class="result-container"></div>
            </div>
        </div>

        <!-- 搜索功能测试 -->
        <div id="search-tab" class="tab-content">
            <div class="section">
                <h2>🔍 音乐搜索</h2>
                <div class="form-group">
                    <label for="search-type">搜索类型:</label>
                    <select id="search-type" onchange="toggleSearchInputs()">
                        <option value="keyword">关键词搜索</option>
                        <option value="id">ID搜索</option>
                        <option value="batch">批量搜索</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="search-query">搜索内容:</label>
                    <input type="text" id="search-query" placeholder="输入搜索关键词或歌曲ID">
                    <small id="search-hint">输入歌手名、歌曲名或专辑名进行搜索</small>
                </div>
                <div class="form-group" id="search-pagination">
                    <label>分页设置:</label>
                    <input type="number" id="search-page" placeholder="页码" value="1" min="1" style="width: 80px;">
                    <input type="number" id="search-pagesize" placeholder="每页数量" value="20" min="1" max="100" style="width: 100px;">
                </div>
                <div class="form-group">
                    <label for="search-sources">指定音源 (可选):</label>
                    <input type="text" id="search-sources" placeholder="例如: qq,kugou">
                </div>
                <div class="button-group">
                    <button onclick="performSearch()" class="btn btn-primary">开始搜索</button>
                    <button onclick="getSearchSuggestions()" class="btn btn-secondary">获取搜索建议</button>
                    <button onclick="getTrendingSearches()" class="btn btn-info">热门搜索</button>
                </div>
                <div id="search-result" class="result-container"></div>
            </div>
        </div>

        <!-- 解锁服务测试 -->
        <div id="unlock-tab" class="tab-content">
            <div class="section">
                <h2>🔓 音乐解锁</h2>
                <div class="form-group">
                    <label for="unlock-ids">歌曲ID列表:</label>
                    <textarea id="unlock-ids" placeholder="输入歌曲ID，每行一个或用逗号分隔&#10;例如:&#10;418602084&#10;186016&#10;185868" rows="4"></textarea>
                    <small>支持多个歌曲ID，每行一个或用逗号分隔</small>
                </div>
                <div class="form-group">
                    <label for="unlock-sources">指定音源 (可选):</label>
                    <input type="text" id="unlock-sources" placeholder="例如: qq,kugou,kuwo">
                </div>
                <div class="form-group">
                    <label for="min-bitrate">最低音质要求:</label>
                    <select id="min-bitrate">
                        <option value="128000">128kbps (标准)</option>
                        <option value="192000">192kbps (较高)</option>
                        <option value="320000" selected>320kbps (极高)</option>
                        <option value="999000">无损</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="detailed-result"> 返回详细信息
                    </label>
                </div>
                <div class="button-group">
                    <button onclick="unlockSongs()" class="btn btn-primary">批量解锁</button>
                    <button onclick="quickUnlock()" class="btn btn-success">快速解锁</button>
                    <button onclick="checkUnlockStatus()" class="btn btn-info">检查状态</button>
                </div>
                <div id="unlock-result" class="result-container"></div>
            </div>
        </div>

        <!-- 音源管理测试 -->
        <div id="sources-tab" class="tab-content">
            <div class="section">
                <h2>🎯 音源管理</h2>
                <div class="button-group">
                    <button onclick="getSources()" class="btn btn-primary">获取音源列表</button>
                    <button onclick="getSourcesWithStatus()" class="btn btn-secondary">获取音源状态</button>
                    <button onclick="testAllSources()" class="btn btn-warning">测试所有音源</button>
                    <button onclick="getSourceStats()" class="btn btn-info">音源统计</button>
                </div>
                <div class="form-group">
                    <label for="test-source-id">测试单个音源:</label>
                    <select id="test-source-id">
                        <option value="qq">QQ音乐</option>
                        <option value="kugou">酷狗音乐</option>
                        <option value="kuwo">酷我音乐</option>
                        <option value="migu">咪咕音乐</option>
                        <option value="joox">JOOX</option>
                        <option value="youtube">YouTube</option>
                    </select>
                    <button onclick="testSingleSource()" class="btn btn-secondary">测试</button>
                </div>
                <div id="sources-result" class="result-container"></div>
            </div>
        </div>

        <!-- API文档 -->
        <div id="docs-tab" class="tab-content">
            <div class="section">
                <h2>📚 API文档</h2>
                <div class="button-group">
                    <button onclick="loadApiDocs()" class="btn btn-primary">加载API文档</button>
                    <button onclick="window.open('/api/docs', '_blank')" class="btn btn-secondary">在新窗口打开</button>
                </div>
                <div id="docs-result" class="result-container"></div>
            </div>
        </div>

        <!-- 页面底部 -->
        <footer class="footer">
            <p>&copy; 2024 音乐解锁服务 | 基于 <a href="https://github.com/UnblockNeteaseMusic/server" target="_blank">UnblockNeteaseMusic</a></p>
            <p>仅供学习和研究使用，请支持正版音乐</p>
        </footer>
    </div>

    <!-- 加载JavaScript -->
    <script src="js/app.js"></script>
</body>
</html>
