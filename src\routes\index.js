/**
 * API路由主入口
 * 统一管理所有API路由
 */

const express = require('express');
const router = express.Router();

// 导入各个路由模块
const songRoutes = require('./songRoutes');
const searchRoutes = require('./searchRoutes');
const unlockRoutes = require('./unlockRoutes');
const sourceRoutes = require('./sourceRoutes');

// 导入中间件
// const { requestLogger } = require('../middleware/logger');

// API版本信息
router.get('/', (req, res) => {
    res.json({
        name: '音乐解锁服务API',
        version: '1.0.0',
        description: '基于UnblockNeteaseMusic的音乐解锁服务后端API',
        endpoints: {
            songs: '/api/song',
            search: '/api/search', 
            unlock: '/api/unlock',
            sources: '/api/sources'
        },
        documentation: '/api/docs',
        health: '/health',
        timestamp: new Date().toISOString()
    });
});

// API文档路由
router.get('/docs', (req, res) => {
    res.json({
        title: '音乐解锁服务API文档',
        version: '1.0.0',
        baseUrl: `${req.protocol}://${req.get('host')}/api`,
        endpoints: [
            {
                group: '歌曲信息',
                routes: [
                    {
                        method: 'GET',
                        path: '/song/:id',
                        description: '根据ID获取歌曲信息和解锁链接',
                        parameters: {
                            path: { id: '歌曲ID' },
                            query: { sources: '指定音源(可选，逗号分隔)' }
                        }
                    },
                    {
                        method: 'GET',
                        path: '/metadata/:id',
                        description: '根据ID获取歌曲元数据(不包含解锁链接)',
                        parameters: {
                            path: { id: '歌曲ID' }
                        }
                    },
                    {
                        method: 'POST',
                        path: '/song/:id/unlock',
                        description: '解锁单首歌曲',
                        parameters: {
                            path: { id: '歌曲ID' },
                            body: { sources: '指定音源(可选)', minBitrate: '最低音质要求(可选)' }
                        }
                    },
                    {
                        method: 'HEAD',
                        path: '/song/:id',
                        description: '检查歌曲是否可用',
                        parameters: {
                            path: { id: '歌曲ID' },
                            query: { sources: '指定音源(可选)' }
                        }
                    }
                ]
            },
            {
                group: '搜索功能',
                routes: [
                    {
                        method: 'POST',
                        path: '/search',
                        description: '统一搜索接口',
                        parameters: {
                            body: {
                                type: '搜索类型(id/keyword/batch)',
                                query: '搜索查询',
                                sources: '指定音源(可选)',
                                page: '页码(可选)',
                                pageSize: '每页大小(可选)'
                            }
                        }
                    },
                    {
                        method: 'GET',
                        path: '/search/id/:songId',
                        description: 'ID搜索',
                        parameters: {
                            path: { songId: '歌曲ID' },
                            query: { sources: '指定音源(可选)', includeUnlockUrl: '是否包含解锁链接(可选)' }
                        }
                    },
                    {
                        method: 'GET',
                        path: '/search/keyword',
                        description: '关键词搜索',
                        parameters: {
                            query: {
                                q: '搜索关键词',
                                sources: '指定音源(可选)',
                                page: '页码(可选)',
                                pageSize: '每页大小(可选)'
                            }
                        }
                    }
                ]
            },
            {
                group: '解锁服务',
                routes: [
                    {
                        method: 'POST',
                        path: '/unlock',
                        description: '批量解锁歌曲',
                        parameters: {
                            body: {
                                songIds: '歌曲ID数组',
                                sources: '指定音源(可选)',
                                minBitrate: '最低音质要求(可选)',
                                detailed: '是否返回详细信息(可选)'
                            }
                        }
                    },
                    {
                        method: 'POST',
                        path: '/unlock/quick',
                        description: '快速解锁(仅返回播放URL)',
                        parameters: {
                            body: {
                                songIds: '歌曲ID数组',
                                sources: '指定音源(可选)'
                            }
                        }
                    },
                    {
                        method: 'GET',
                        path: '/unlock/status/:id',
                        description: '检查解锁状态',
                        parameters: {
                            path: { id: '歌曲ID' },
                            query: { sources: '指定音源(可选)' }
                        }
                    }
                ]
            },
            {
                group: '音源管理',
                routes: [
                    {
                        method: 'GET',
                        path: '/sources',
                        description: '获取所有可用音源列表',
                        parameters: {
                            query: { includeStatus: '是否包含状态信息(可选)' }
                        }
                    },
                    {
                        method: 'GET',
                        path: '/sources/:sourceId',
                        description: '获取单个音源详细信息',
                        parameters: {
                            path: { sourceId: '音源ID' }
                        }
                    },
                    {
                        method: 'POST',
                        path: '/sources/:sourceId/test',
                        description: '测试音源可用性',
                        parameters: {
                            path: { sourceId: '音源ID' },
                            body: { testSongId: '测试歌曲ID(可选)' }
                        }
                    }
                ]
            }
        ],
        examples: {
            searchByKeyword: {
                url: '/api/search',
                method: 'POST',
                body: {
                    type: 'keyword',
                    query: '周杰伦',
                    page: 1,
                    pageSize: 20
                }
            },
            unlockSong: {
                url: '/api/song/418602084',
                method: 'GET',
                query: 'sources=qq,kugou'
            },
            batchUnlock: {
                url: '/api/unlock',
                method: 'POST',
                body: {
                    songIds: [418602084, 186016],
                    sources: ['qq', 'kugou'],
                    minBitrate: 320000
                }
            }
        }
    });
});

// 挂载各个路由模块
router.use('/song', songRoutes);
router.use('/metadata', songRoutes); // 元数据路由复用歌曲路由
router.use('/search', searchRoutes);
router.use('/unlock', unlockRoutes);
router.use('/sources', sourceRoutes);

module.exports = router;
