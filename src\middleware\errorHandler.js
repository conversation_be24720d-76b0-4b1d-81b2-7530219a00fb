/**
 * 错误处理中间件模块
 * 统一处理应用中的各种错误和异常情况
 */

const { logError } = require('./logger');
// const { internalError, serviceUnavailable } = require('../utils/response');
const { HTTP_STATUS, ERROR_CODES } = require('../utils/constants');

/**
 * 自定义错误类
 */
class AppError extends Error {
    constructor(message, statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR, errorCode = ERROR_CODES.INTERNAL_ERROR) {
        super(message);
        this.statusCode = statusCode;
        this.errorCode = errorCode;
        this.isOperational = true;

        Error.captureStackTrace(this, this.constructor);
    }
}

/**
 * 业务错误类
 */
class BusinessError extends AppError {
    constructor(message, errorCode) {
        super(message, HTTP_STATUS.BAD_REQUEST, errorCode);
    }
}

/**
 * 资源未找到错误类
 */
class NotFoundError extends AppError {
    constructor(message = '资源未找到') {
        super(message, HTTP_STATUS.NOT_FOUND, ERROR_CODES.SONG_NOT_FOUND);
    }
}

/**
 * 验证错误类
 */
class ValidationError extends AppError {
    constructor(message, details = null) {
        super(message, HTTP_STATUS.BAD_REQUEST, ERROR_CODES.VALIDATION_ERROR);
        this.details = details;
    }
}

/**
 * 服务不可用错误类
 */
class ServiceUnavailableError extends AppError {
    constructor(message = '服务暂时不可用') {
        super(message, HTTP_STATUS.SERVICE_UNAVAILABLE, ERROR_CODES.SOURCE_UNAVAILABLE);
    }
}

/**
 * 处理UnblockNeteaseMusic相关错误
 * @param {Error} error - 原始错误
 * @returns {AppError} 转换后的应用错误
 */
function handleUnblockError(error) {
    const message = error.message || '音乐解锁服务出错';
  
    // 根据错误消息判断错误类型
    if (message.includes('timeout') || message.includes('TIMEOUT')) {
        return new ServiceUnavailableError('音乐服务响应超时');
    }
  
    if (message.includes('not found') || message.includes('NOT_FOUND')) {
        return new NotFoundError('歌曲未找到');
    }
  
    if (message.includes('network') || message.includes('NETWORK')) {
        return new ServiceUnavailableError('网络连接错误');
    }
  
    return new AppError(message, HTTP_STATUS.INTERNAL_SERVER_ERROR, ERROR_CODES.UNLOCK_FAILED);
}

/**
 * 处理数据库错误
 * @param {Error} error - 数据库错误
 * @returns {AppError} 转换后的应用错误
 */
// function handleDatabaseError(error) {
//     logError(error, { type: 'database_error' });
//     return new AppError('数据库操作失败', HTTP_STATUS.INTERNAL_SERVER_ERROR, ERROR_CODES.INTERNAL_ERROR);
// }

/**
 * 处理网络请求错误
 * @param {Error} error - 网络错误
 * @returns {AppError} 转换后的应用错误
 */
function handleNetworkError(error) {
    if (error.code === 'ECONNREFUSED') {
        return new ServiceUnavailableError('无法连接到音乐服务');
    }
  
    if (error.code === 'ETIMEDOUT') {
        return new ServiceUnavailableError('请求超时');
    }
  
    return new ServiceUnavailableError('网络请求失败');
}

/**
 * 错误处理中间件
 * @param {Error} err - 错误对象
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
function errorHandler(err, req, res) {
    let error = err;

    // 如果不是自定义错误，进行转换
    if (!(error instanceof AppError)) {
    // 处理不同类型的错误
        if (error.name === 'ValidationError') {
            error = new ValidationError(error.message, error.details);
        } else if (error.name === 'CastError') {
            error = new ValidationError('无效的参数格式');
        } else if (error.code && error.code.startsWith('E')) {
            error = handleNetworkError(error);
        } else if (error.message && error.message.includes('unblock')) {
            error = handleUnblockError(error);
        } else {
            error = new AppError(error.message || '服务器内部错误');
        }
    }

    // 记录错误日志
    logError(error, {
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: req.id
    });

    // 发送错误响应
    res.status(error.statusCode).json({
        code: error.statusCode,
        message: error.message,
        errorCode: error.errorCode,
        timestamp: new Date().toISOString(),
        ...(error.details && { details: error.details }),
        ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
    });
}

/**
 * 404错误处理中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
function notFoundHandler(req, res, next) {
    const error = new NotFoundError(`路由 ${req.method} ${req.url} 未找到`);
    next(error);
}

/**
 * 异步错误包装器
 * 用于包装异步路由处理函数，自动捕获Promise拒绝
 * @param {Function} fn - 异步函数
 * @returns {Function} 包装后的函数
 */
function asyncHandler(fn) {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
}

/**
 * 进程级错误处理
 */
function setupProcessErrorHandlers() {
    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
        logError(error, { type: 'uncaught_exception' });
        console.error('未捕获的异常:', error);
        process.exit(1);
    });

    // 处理未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
        logError(new Error(reason), { 
            type: 'unhandled_rejection',
            promise: promise.toString()
        });
        console.error('未处理的Promise拒绝:', reason);
    });
}

module.exports = {
    AppError,
    BusinessError,
    NotFoundError,
    ValidationError,
    ServiceUnavailableError,
    errorHandler,
    notFoundHandler,
    asyncHandler,
    setupProcessErrorHandlers
};
