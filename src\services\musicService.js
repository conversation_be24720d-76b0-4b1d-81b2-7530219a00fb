/**
 * 音乐元数据服务模块
 * 提供音乐信息获取、元数据处理等功能
 */

const { unlockSong } = require('./unlockService');
const { logError, logPerformance, logBusiness, logInfo } = require('../middleware/logger');
const { NotFoundError, ServiceUnavailableError } = require('../middleware/errorHandler');
const { QUALITY_DISPLAY_NAMES } = require('../utils/constants');

/**
 * 获取歌曲完整信息（包含解锁链接）
 * @param {number|string} songId - 歌曲ID
 * @param {Array<string>} sources - 指定音源
 * @returns {Promise<Object>} 歌曲完整信息
 */
async function getSongInfo(songId, sources = null) {
    const startTime = Date.now();
  
    try {
        logBusiness('获取歌曲信息', { songId, sources });

        // 首先尝试解锁歌曲获取基础信息
        const unlockResult = await unlockSong(songId, sources);
    
        // 尝试获取更详细的元数据
        const detailedMetadata = await getDetailedMetadata(songId, unlockResult);
    
        // 合并信息
        const songInfo = {
            id: parseInt(songId),
            title: detailedMetadata.title || unlockResult.metadata.title || '未知歌曲',
            artist: detailedMetadata.artist || unlockResult.metadata.artist || '未知艺术家',
            album: detailedMetadata.album || unlockResult.metadata.album || '未知专辑',
            duration: detailedMetadata.duration || unlockResult.metadata.duration || 0,
            cover: detailedMetadata.cover || unlockResult.metadata.cover || null,
      
            // 音频信息
            audio: {
                url: unlockResult.url,
                bitrate: unlockResult.bitrate,
                quality: getQualityLevel(unlockResult.bitrate),
                format: unlockResult.type,
                size: unlockResult.size,
                md5: unlockResult.md5
            },
      
            // 音源信息
            source: {
                id: unlockResult.source,
                name: unlockResult.sourceName,
                available: true
            },
      
            // 时间戳
            timestamp: unlockResult.timestamp
        };

        const duration = Date.now() - startTime;
        logPerformance('获取歌曲信息', duration, { songId, success: true });
    
        logBusiness('歌曲信息获取成功', { 
            songId, 
            title: songInfo.title,
            source: songInfo.source.id 
        });

        return songInfo;

    } catch (error) {
        const duration = Date.now() - startTime;
        logPerformance('获取歌曲信息失败', duration, { songId, error: error.message });
    
        if (error instanceof NotFoundError) {
            throw error;
        }
    
        logError(error, { context: 'get_song_info', songId });
        throw new ServiceUnavailableError(`获取歌曲 ${songId} 信息失败: ${error.message}`);
    }
}

/**
 * 获取歌曲元数据（不包含解锁链接）
 * @param {number|string} songId - 歌曲ID
 * @returns {Promise<Object>} 歌曲元数据
 */
async function getSongMetadata(songId) {
    const startTime = Date.now();
  
    try {
        logBusiness('获取歌曲元数据', { songId });

        // 尝试从网易云API获取元数据
        const metadata = await fetchNeteaseMetadata(songId);
    
        if (!metadata) {
            // 如果网易云API无法获取，尝试通过解锁服务获取
            const unlockResult = await unlockSong(songId);
            return extractMetadataFromUnlock(unlockResult);
        }

        const duration = Date.now() - startTime;
        logPerformance('获取歌曲元数据', duration, { songId, success: true });

        return metadata;

    } catch (error) {
        const duration = Date.now() - startTime;
        logPerformance('获取歌曲元数据失败', duration, { songId, error: error.message });
    
        logError(error, { context: 'get_song_metadata', songId });
        throw new ServiceUnavailableError(`获取歌曲 ${songId} 元数据失败: ${error.message}`);
    }
}

/**
 * 从网易云API获取元数据
 * @param {number|string} songId - 歌曲ID
 * @returns {Promise<Object|null>} 元数据或null
 */
async function fetchNeteaseMetadata(songId) {
    // 这里可以实现网易云API调用
    // 由于网易云API可能需要特殊处理，暂时返回null
    // 实际项目中可以集成网易云音乐API
    logInfo('Netease metadata fetch not implemented', { songId });
    return null;
}

/**
 * 获取详细元数据
 * @param {number|string} songId - 歌曲ID
 * @param {Object} unlockResult - 解锁结果
 * @returns {Promise<Object>} 详细元数据
 */
async function getDetailedMetadata(songId, unlockResult) {
    try {
    // 尝试从网易云获取详细信息
        const neteaseMetadata = await fetchNeteaseMetadata(songId);
    
        if (neteaseMetadata) {
            return neteaseMetadata;
        }
    
        // 如果无法从网易云获取，使用解锁结果中的元数据
        return unlockResult.metadata;
    
    } catch (error) {
        logError(error, { context: 'get_detailed_metadata', songId });
        return unlockResult.metadata;
    }
}

/**
 * 从解锁结果中提取元数据
 * @param {Object} unlockResult - 解锁结果
 * @returns {Object} 元数据
 */
function extractMetadataFromUnlock(unlockResult) {
    return {
        id: unlockResult.songId,
        title: unlockResult.metadata.title || '未知歌曲',
        artist: unlockResult.metadata.artist || '未知艺术家',
        album: unlockResult.metadata.album || '未知专辑',
        duration: unlockResult.metadata.duration || 0,
        cover: unlockResult.metadata.cover || null,
        source: unlockResult.source,
        timestamp: unlockResult.timestamp
    };
}

/**
 * 根据比特率获取音质等级
 * @param {number} bitrate - 比特率
 * @returns {string} 音质等级描述
 */
function getQualityLevel(bitrate) {
    if (bitrate >= 999000) return QUALITY_DISPLAY_NAMES[999000] || '无损';
    if (bitrate >= 320000) return QUALITY_DISPLAY_NAMES[320000] || '极高';
    if (bitrate >= 192000) return QUALITY_DISPLAY_NAMES[192000] || '较高';
    if (bitrate >= 128000) return QUALITY_DISPLAY_NAMES[128000] || '标准';
    return '低质量';
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
function formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '未知';
  
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
  
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
  
    return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * 格式化时长
 * @param {number} duration - 时长（秒）
 * @returns {string} 格式化后的时长
 */
function formatDuration(duration) {
    if (!duration || duration === 0) return '00:00';
  
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
  
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

/**
 * 验证歌曲ID格式
 * @param {any} songId - 歌曲ID
 * @returns {boolean} 是否有效
 */
function isValidSongId(songId) {
    const id = parseInt(songId);
    return !isNaN(id) && id > 0 && id <= Number.MAX_SAFE_INTEGER;
}

/**
 * 批量获取歌曲信息
 * @param {Array<number|string>} songIds - 歌曲ID列表
 * @param {Array<string>} sources - 指定音源
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 批量结果
 */
async function getSongsInfoBatch(songIds, sources = null, options = {}) {
    const startTime = Date.now();
    const { includeMetadata = true } = options;
  
    logBusiness('批量获取歌曲信息', { count: songIds.length, sources, options });

    const results = {
        success: [],
        failed: [],
        total: songIds.length,
        successCount: 0,
        failedCount: 0
    };

    // 并发处理，限制并发数
    const concurrency = 3;
    const chunks = [];
    for (let i = 0; i < songIds.length; i += concurrency) {
        chunks.push(songIds.slice(i, i + concurrency));
    }

    for (const chunk of chunks) {
        const promises = chunk.map(async (songId) => {
            try {
                const songInfo = includeMetadata ? 
                    await getSongInfo(songId, sources) : 
                    await getSongMetadata(songId);
          
                results.success.push(songInfo);
                results.successCount++;
        
            } catch (error) {
                results.failed.push({
                    songId,
                    error: error.message,
                    errorCode: error.errorCode || 'GET_INFO_FAILED'
                });
                results.failedCount++;
            }
        });

        await Promise.all(promises);
    }

    const duration = Date.now() - startTime;
    logPerformance('批量获取歌曲信息完成', duration, {
        total: results.total,
        success: results.successCount,
        failed: results.failedCount
    });

    return results;
}

module.exports = {
    getSongInfo,
    getSongMetadata,
    getSongsInfoBatch,
    getQualityLevel,
    formatFileSize,
    formatDuration,
    isValidSongId
};
