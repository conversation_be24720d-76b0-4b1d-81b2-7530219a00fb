/**
 * 歌曲相关路由
 */

const express = require('express');
const router = express.Router();

// 导入控制器
const {
    getSong,
    getSongMetadataOnly,
    unlockSongOnly,
    checkSongAvailability,
    getSongSources,
    getSongDetail
} = require('../controllers/songController');

// 导入验证中间件
const { validators } = require('../middleware/validator');

// 歌曲信息路由
router.get('/:id', validators.validateGetSong, getSong);

// 歌曲元数据路由（不包含解锁链接）
router.get('/:id/metadata', validators.validateGetSong, getSongMetadataOnly);

// 解锁单首歌曲
router.post('/:id/unlock', validators.validateGetSong, unlockSongOnly);

// 检查歌曲可用性（HEAD请求）
router.head('/:id', validators.validateGetSong, checkSongAvailability);

// 获取歌曲的所有可用音源
router.get('/:id/sources', validators.validateGetSong, getSongSources);

// 获取歌曲详细信息
router.get('/:id/detail', validators.validateGetSong, getSongDetail);

module.exports = router;
