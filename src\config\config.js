/**
 * 应用配置管理模块
 * 负责加载和管理所有环境变量和应用配置
 */

require('dotenv').config();

const config = {
    // 服务器基础配置
    server: {
        port: process.env.PORT || 3000,
        host: process.env.HOST || 'localhost',
        env: process.env.NODE_ENV || 'development'
    },

    // 日志配置
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        fileEnabled: process.env.LOG_FILE_ENABLED === 'true',
        consoleEnabled: process.env.LOG_CONSOLE_ENABLED !== 'false',
        maxFiles: '14d',
        maxSize: '20m'
    },

    // UnblockNeteaseMusic 配置
    music: {
    // 音源优先级配置
        sources: process.env.MUSIC_SOURCES ? 
            process.env.MUSIC_SOURCES.split(',').map(s => s.trim()) : 
            ['qq', 'kugou', 'kuwo', 'migu'],
    
        // 网易云音乐配置
        neteaseCookie: process.env.NETEASE_COOKIE || '',
        enableFlac: process.env.ENABLE_FLAC === 'true',
        enableLocalVip: process.env.ENABLE_LOCAL_VIP || false,
        minBitrate: parseInt(process.env.MIN_BR) || 128000,
    
        // 第三方音源配置
        qqCookie: process.env.QQ_COOKIE || '',
        miguCookie: process.env.MIGU_COOKIE || '',
        jooxCookie: process.env.JOOX_COOKIE || '',
        youtubeKey: process.env.YOUTUBE_KEY || '',
    
        // 功能开关
        enableSearchAlbum: process.env.ENABLE_SEARCH_ALBUM === 'true',
        followSourceOrder: process.env.FOLLOW_SOURCE_ORDER === 'true',
        blockAds: process.env.BLOCK_ADS === 'true'
    },

    // 安全配置
    security: {
        corsOrigin: process.env.CORS_ORIGIN || '*',
        rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
        rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
    },

    // API配置
    api: {
        prefix: '/api',
        version: 'v1',
        timeout: 30000, // 30秒超时
        maxSearchResults: 50 // 最大搜索结果数量
    }
};

/**
 * 验证必要的配置项
 */
function validateConfig() {
    const errors = [];

    // 验证端口号
    if (isNaN(config.server.port) || config.server.port < 1 || config.server.port > 65535) {
        errors.push('无效的端口号配置');
    }

    // 验证音源配置
    const validSources = ['qq', 'kugou', 'kuwo', 'migu', 'joox', 'youtube', 'ytdlp', 'bilibili'];
    const invalidSources = config.music.sources.filter(source => !validSources.includes(source));
    if (invalidSources.length > 0) {
        errors.push(`无效的音源配置: ${invalidSources.join(', ')}`);
    }

    if (errors.length > 0) {
        throw new Error(`配置验证失败:\n${errors.join('\n')}`);
    }
}

// 验证配置
validateConfig();

module.exports = config;
